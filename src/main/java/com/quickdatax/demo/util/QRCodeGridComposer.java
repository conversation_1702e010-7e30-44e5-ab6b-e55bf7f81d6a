package com.quickdatax.demo.util;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * 二维码网格合并工具类
 * 用于将多个二维码图片按照指定的网格格式合并成一张大图，方便打印和剪裁
 * 
 * <AUTHOR>
 * @date 2024/07/04
 */
@Slf4j
public class QRCodeGridComposer {
    
    // 网格配置
    private static final int COLS_PER_ROW = 5;  // 每行5个
    private static final int ROWS_PER_PAGE = 15; // 每列15个
    private static final int MAX_QRCODES_PER_PAGE = COLS_PER_ROW * ROWS_PER_PAGE; // 每页最多75个
    
    // 图片尺寸配置（单位：像素）
    private static final int QRCODE_SIZE = 200;        // 单个二维码尺寸
    private static final int MARGIN = 20;              // 二维码之间的间距
    private static final int PAGE_MARGIN = 40;         // 页面边距
    
    // 计算的尺寸
    private static final int CELL_WIDTH = QRCODE_SIZE + MARGIN;
    private static final int CELL_HEIGHT = QRCODE_SIZE + MARGIN;
    private static final int PAGE_WIDTH = PAGE_MARGIN * 2 + COLS_PER_ROW * CELL_WIDTH - MARGIN;
    private static final int PAGE_HEIGHT = PAGE_MARGIN * 2 + ROWS_PER_PAGE * CELL_HEIGHT - MARGIN;
    
    /**
     * 将二维码列表合并成网格图片
     * 
     * @param qrCodeBase64List Base64编码的二维码图片列表
     * @return 合并后的图片Base64编码
     */
    public static String mergeQRCodesToGrid(List<String> qrCodeBase64List) {
        if (qrCodeBase64List == null || qrCodeBase64List.isEmpty()) {
            throw new IllegalArgumentException("二维码列表不能为空");
        }
        
        if (qrCodeBase64List.size() > MAX_QRCODES_PER_PAGE) {
            throw new IllegalArgumentException(
                String.format("二维码数量超过限制，最多支持%d个（%d行×%d列）", 
                    MAX_QRCODES_PER_PAGE, ROWS_PER_PAGE, COLS_PER_ROW)
            );
        }
        
        try {
            // 创建画布
            BufferedImage gridImage = new BufferedImage(PAGE_WIDTH, PAGE_HEIGHT, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = gridImage.createGraphics();
            
            // 设置渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            
            // 填充白色背景
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, PAGE_WIDTH, PAGE_HEIGHT);
            
            // 绘制网格线（可选，用于剪裁参考）
            drawGridLines(g2d);
            
            // 逐个绘制二维码
            for (int i = 0; i < qrCodeBase64List.size(); i++) {
                String base64Data = qrCodeBase64List.get(i);
                BufferedImage qrCodeImage = decodeBase64ToImage(base64Data);
                
                if (qrCodeImage != null) {
                    // 计算位置
                    int row = i / COLS_PER_ROW;
                    int col = i % COLS_PER_ROW;
                    
                    int x = PAGE_MARGIN + col * CELL_WIDTH;
                    int y = PAGE_MARGIN + row * CELL_HEIGHT;
                    
                    // 缩放二维码到指定尺寸
                    Image scaledImage = qrCodeImage.getScaledInstance(QRCODE_SIZE, QRCODE_SIZE, Image.SCALE_SMOOTH);
                    g2d.drawImage(scaledImage, x, y, null);
                    
                    log.debug("绘制二维码 {} 到位置 ({}, {})", i + 1, x, y);
                }
            }
            
            g2d.dispose();
            
            // 转换为Base64
            return encodeImageToBase64(gridImage);
            
        } catch (Exception e) {
            log.error("合并二维码网格失败", e);
            throw new RuntimeException("合并二维码网格失败: " + e.getMessage());
        }
    }
    
    /**
     * 绘制网格线，方便剪裁
     */
    private static void drawGridLines(Graphics2D g2d) {
        g2d.setColor(new Color(200, 200, 200)); // 浅灰色
        g2d.setStroke(new BasicStroke(1.0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 
                                     10.0f, new float[]{5.0f, 5.0f}, 0.0f)); // 虚线
        
        // 绘制垂直线
        for (int col = 0; col <= COLS_PER_ROW; col++) {
            int x = PAGE_MARGIN + col * CELL_WIDTH - MARGIN / 2;
            g2d.drawLine(x, PAGE_MARGIN - 10, x, PAGE_HEIGHT - PAGE_MARGIN + 10);
        }
        
        // 绘制水平线
        for (int row = 0; row <= ROWS_PER_PAGE; row++) {
            int y = PAGE_MARGIN + row * CELL_HEIGHT - MARGIN / 2;
            g2d.drawLine(PAGE_MARGIN - 10, y, PAGE_WIDTH - PAGE_MARGIN + 10, y);
        }
    }
    
    /**
     * 将Base64字符串解码为BufferedImage
     */
    private static BufferedImage decodeBase64ToImage(String base64Data) {
        try {
            byte[] imageBytes = Base64.getDecoder().decode(base64Data);
            return ImageIO.read(new ByteArrayInputStream(imageBytes));
        } catch (Exception e) {
            log.error("解码Base64图片失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 将BufferedImage编码为Base64字符串
     */
    private static String encodeImageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
    
    /**
     * 获取网格配置信息
     */
    public static String getGridInfo() {
        return String.format("网格配置: %d行×%d列，最多%d个二维码，单个尺寸%d×%d像素，总尺寸%d×%d像素", 
                           ROWS_PER_PAGE, COLS_PER_ROW, MAX_QRCODES_PER_PAGE, 
                           QRCODE_SIZE, QRCODE_SIZE, PAGE_WIDTH, PAGE_HEIGHT);
    }
}
