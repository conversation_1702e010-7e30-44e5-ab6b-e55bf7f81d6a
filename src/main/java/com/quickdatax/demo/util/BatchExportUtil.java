package com.quickdatax.demo.util;

import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 批量导出工具类
 * <AUTHOR>
 * @date 2024/07/04
 */
@Slf4j
public class BatchExportUtil {
    
    /**
     * 创建ZIP文件包含所有二维码图片
     * 
     * @param qrCodeDataList 二维码数据列表，每个元素包含clientToken和base64Data
     * @param zipFileName ZIP文件名
     * @return ResponseEntity包含ZIP文件数据
     */
    public static ResponseEntity<byte[]> createQRCodeZip(List<QRCodeData> qrCodeDataList, String zipFileName) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zos = new ZipOutputStream(baos);
            
            for (QRCodeData qrCodeData : qrCodeDataList) {
                try {
                    // 解码Base64图片数据
                    byte[] imageBytes = Base64.decode(qrCodeData.getBase64Data());
                    
                    // 创建ZIP条目
                    String fileName = "qrcode_" + sanitizeFileName(qrCodeData.getClientToken()) + ".png";
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zos.putNextEntry(zipEntry);
                    
                    // 写入图片数据
                    zos.write(imageBytes);
                    zos.closeEntry();
                    
                    log.debug("已添加文件到ZIP: {}", fileName);
                    
                } catch (Exception e) {
                    log.warn("添加文件到ZIP失败，clientToken: {}, 错误: {}", 
                        qrCodeData.getClientToken(), e.getMessage());
                }
            }
            
            zos.close();
            byte[] zipBytes = baos.toByteArray();
            
            log.info("ZIP文件创建成功，文件大小: {} bytes, 包含图片数量: {}", 
                zipBytes.length, qrCodeDataList.size());
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", zipFileName);
            headers.setContentLength(zipBytes.length);
            
            return ResponseEntity.ok()
                .headers(headers)
                .body(zipBytes);
                
        } catch (IOException e) {
            log.error("创建ZIP文件失败", e);
            throw new RuntimeException("创建ZIP文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 清理文件名，移除不安全字符
     */
    private static String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unknown";
        }
        
        // 替换不安全字符
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_")
                      .replaceAll("_{2,}", "_") // 多个下划线替换为单个
                      .substring(0, Math.min(fileName.length(), 50)); // 限制长度
    }
    
    /**
     * 二维码数据类
     */
    public static class QRCodeData {
        private String clientToken;
        private String base64Data;
        
        public QRCodeData() {}
        
        public QRCodeData(String clientToken, String base64Data) {
            this.clientToken = clientToken;
            this.base64Data = base64Data;
        }
        
        public String getClientToken() {
            return clientToken;
        }
        
        public void setClientToken(String clientToken) {
            this.clientToken = clientToken;
        }
        
        public String getBase64Data() {
            return base64Data;
        }
        
        public void setBase64Data(String base64Data) {
            this.base64Data = base64Data;
        }
    }
    
    /**
     * 批量导出请求VO
     */
    public static class BatchExportReqVO {
        private List<QRCodeData> qrCodes;
        private String zipFileName;
        
        public BatchExportReqVO() {}
        
        public List<QRCodeData> getQrCodes() {
            return qrCodes;
        }
        
        public void setQrCodes(List<QRCodeData> qrCodes) {
            this.qrCodes = qrCodes;
        }
        
        public String getZipFileName() {
            return zipFileName;
        }
        
        public void setZipFileName(String zipFileName) {
            this.zipFileName = zipFileName;
        }
    }
}
