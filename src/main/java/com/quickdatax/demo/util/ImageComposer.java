package com.quickdatax.demo.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.resource.ResourceUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * 图片合成工具类
 * 专门用于将小程序码合成到绿色模板图片上
 * <AUTHOR>
 * @date 2024/07/04
 */
@Slf4j
public class ImageComposer {
    
    // 模板图片配置（根据实际模板图片调整这些值）
    private static final String TEMPLATE_PATH = "static/template.png";
    
    // 二维码区域配置（左侧空白矩形）
    private static final int QR_CODE_X = 25;      // 二维码X坐标
    private static final int QR_CODE_Y = 25;      // 二维码Y坐标
    private static final int QR_CODE_WIDTH = 115; // 二维码宽度
    private static final int QR_CODE_HEIGHT = 115; // 二维码高度
    
    // 文字配置
    private static final int TEXT_X = 180;        // "ID:"文字X坐标
    private static final int TEXT_Y = 120;        // "ID:"文字Y坐标
    private static final String FONT_NAME = "Microsoft YaHei"; // 字体名称
    private static final int FONT_SIZE = 24;      // 字体大小
    private static final Color TEXT_COLOR = Color.WHITE; // 文字颜色
    
    /**
     * 将小程序码合成到模板图片上
     * 
     * @param qrCodeBase64 小程序码的Base64编码
     * @param clientToken 客户端Token
     * @return 合成后的图片Base64编码
     */
    public static String composeQrCodeWithTemplate(String qrCodeBase64, String clientToken) {
        try {
            log.debug("开始合成图片，clientToken: {}", clientToken);
            
            // 加载模板图片
            BufferedImage templateImage = loadTemplateImage();
            if (templateImage == null) {
                throw new RuntimeException("无法加载模板图片");
            }
            
            // 解码小程序码
            BufferedImage qrCodeImage = decodeQrCodeImage(qrCodeBase64);
            if (qrCodeImage == null) {
                throw new RuntimeException("无法解码小程序码图片");
            }
            
            // 创建合成图片
            BufferedImage composedImage = createComposedImage(templateImage, qrCodeImage, clientToken);
            
            // 转换为Base64
            String result = encodeImageToBase64(composedImage);
            
            log.debug("图片合成完成，clientToken: {}", clientToken);
            return result;
            
        } catch (Exception e) {
            log.error("合成图片失败，clientToken: {}", clientToken, e);
            throw new RuntimeException("合成图片失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 加载模板图片
     */
    private static BufferedImage loadTemplateImage() {
        try {
            InputStream templateStream = ResourceUtil.getStream(TEMPLATE_PATH);
            if (templateStream == null) {
                log.warn("模板图片不存在: {}", TEMPLATE_PATH);
                return createDefaultTemplate();
            }
            return ImageIO.read(templateStream);
        } catch (Exception e) {
            log.warn("加载模板图片失败，使用默认模板: {}", e.getMessage());
            return createDefaultTemplate();
        }
    }
    
    /**
     * 创建默认模板（如果模板图片不存在）
     */
    private static BufferedImage createDefaultTemplate() {
        // 创建一个绿色背景的默认模板
        BufferedImage template = new BufferedImage(400, 165, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = template.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 绘制绿色背景
        g2d.setColor(new Color(46, 204, 113)); // 绿色背景
        g2d.fillRoundRect(0, 0, 400, 165, 20, 20);
        
        // 绘制左侧白色矩形区域
        g2d.setColor(Color.WHITE);
        g2d.fillRect(QR_CODE_X, QR_CODE_Y, QR_CODE_WIDTH, QR_CODE_HEIGHT);
        
        // 绘制标题文字
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font(FONT_NAME, Font.BOLD, 28));
        g2d.drawString("计件宝365", 180, 50);
        g2d.drawString("(扫码绑定)", 320, 70);
        
        // 绘制虚线
        g2d.setStroke(new BasicStroke(2, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL, 0, new float[]{5}, 0));
        g2d.drawLine(180, 80, 380, 80);
        
        // 绘制ID标签
        g2d.setStroke(new BasicStroke(1));
        g2d.setFont(new Font(FONT_NAME, Font.BOLD, FONT_SIZE));
        g2d.drawString("ID:", 180, 120);
        
        g2d.dispose();
        return template;
    }
    
    /**
     * 解码小程序码图片
     */
    private static BufferedImage decodeQrCodeImage(String qrCodeBase64) {
        try {
            byte[] qrCodeBytes = Base64.decode(qrCodeBase64);
            return ImageIO.read(new ByteArrayInputStream(qrCodeBytes));
        } catch (Exception e) {
            log.error("解码小程序码失败", e);
            return null;
        }
    }
    
    /**
     * 创建合成图片
     */
    private static BufferedImage createComposedImage(BufferedImage templateImage, BufferedImage qrCodeImage, String clientToken) {
        BufferedImage composedImage = new BufferedImage(
            templateImage.getWidth(), 
            templateImage.getHeight(), 
            BufferedImage.TYPE_INT_RGB
        );
        
        Graphics2D g2d = composedImage.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        
        // 绘制模板图片
        g2d.drawImage(templateImage, 0, 0, null);
        
        // 绘制二维码，完全覆盖左侧空白矩形区域
        g2d.drawImage(qrCodeImage, QR_CODE_X, QR_CODE_Y, QR_CODE_WIDTH, QR_CODE_HEIGHT, null);
        
        // 绘制clientToken文字
        drawClientToken(g2d, clientToken);
        
        g2d.dispose();
        return composedImage;
    }
    
    /**
     * 绘制clientToken文字
     */
    private static void drawClientToken(Graphics2D g2d, String clientToken) {
        // 设置字体（与"ID:"标题保持一致）
        Font font = new Font(FONT_NAME, Font.BOLD, FONT_SIZE);
        g2d.setFont(font);
        g2d.setColor(TEXT_COLOR);
        
        // 计算"ID:"文字的宽度
        FontMetrics fm = g2d.getFontMetrics();
        int idWidth = fm.stringWidth("ID:");
        
        // 在"ID:"右侧绘制clientToken，留适当间距
        int tokenX = TEXT_X + idWidth + 10;  // 10像素间距
        int tokenY = TEXT_Y;
        
        // 如果clientToken太长，进行截断处理
        String displayToken = clientToken;
        int maxWidth = 180; // 最大宽度
        if (fm.stringWidth(displayToken) > maxWidth) {
            while (fm.stringWidth(displayToken + "...") > maxWidth && displayToken.length() > 1) {
                displayToken = displayToken.substring(0, displayToken.length() - 1);
            }
            displayToken += "...";
        }
        
        g2d.drawString(displayToken, tokenX, tokenY);
    }
    
    /**
     * 将图片编码为Base64
     */
    private static String encodeImageToBase64(BufferedImage image) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            return Base64.encode(outputStream.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("图片编码失败", e);
        }
    }
    
    /**
     * 设置二维码区域参数（用于动态调整）
     */
    public static class QrCodeArea {
        public static final int X = QR_CODE_X;
        public static final int Y = QR_CODE_Y;
        public static final int WIDTH = QR_CODE_WIDTH;
        public static final int HEIGHT = QR_CODE_HEIGHT;
    }
    
    /**
     * 设置文字区域参数（用于动态调整）
     */
    public static class TextArea {
        public static final int X = TEXT_X;
        public static final int Y = TEXT_Y;
        public static final String FONT = FONT_NAME;
        public static final int SIZE = FONT_SIZE;
        public static final Color COLOR = TEXT_COLOR;
    }
}
