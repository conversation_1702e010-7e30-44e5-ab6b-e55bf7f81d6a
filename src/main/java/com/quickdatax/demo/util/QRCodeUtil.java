package com.quickdatax.demo.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;

import java.awt.*;
import java.io.ByteArrayOutputStream;

/**
 * 二维码工具类
 * <AUTHOR>
 */
public class QRCodeUtil {

    /**
     * 生成二维码Base64字符串
     *
     * @param content 二维码内容
     * @param width 宽度
     * @param height 高度
     * @return Base64编码的二维码图片
     */
    public static String generateQrCodeBase64(String content, int width, int height) {
        try {
            QrConfig config = new QrConfig(width, height);
            config.setMargin(1);
            config.setForeColor(Color.BLACK);
            config.setBackColor(Color.WHITE);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            QrCodeUtil.generate(content, config, "png", outputStream);

            byte[] imageBytes = outputStream.toByteArray();
            return Base64.encode(imageBytes);
        } catch (Exception e) {
            throw new RuntimeException("生成二维码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成二维码Base64字符串（默认300x300）
     *
     * @param content 二维码内容
     * @return Base64编码的二维码图片
     */
    public static String generateQrCodeBase64(String content) {
        return generateQrCodeBase64(content, 300, 300);
    }
}
