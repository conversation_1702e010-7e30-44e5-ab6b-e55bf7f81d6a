package com.quickdatax.demo.web.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.quickdatax.demo.util.ImageComposer;
import com.quickdatax.demo.web.config.WechatProperties;
import com.quickdatax.demo.web.controller.vo.QRCodeReqVO;
import com.quickdatax.demo.web.controller.vo.QRCodeRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 微信小程序服务
 * <AUTHOR>
 * @date 2024/07/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatService {
    
    private final WechatProperties wechatProperties;
    private final WebClient webClient = WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    
    // Access Token缓存
    private static final ConcurrentHashMap<String, AccessTokenCache> ACCESS_TOKEN_CACHE = new ConcurrentHashMap<>();
    
    /**
     * Access Token缓存对象
     */
    private static class AccessTokenCache {
        private String accessToken;
        private LocalDateTime expireTime;
        
        public AccessTokenCache(String accessToken, LocalDateTime expireTime) {
            this.accessToken = accessToken;
            this.expireTime = expireTime;
        }
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expireTime);
        }
        
        public String getAccessToken() {
            return accessToken;
        }
    }
    
    /**
     * 获取Access Token
     */
    public String getAccessToken() {
        String cacheKey = wechatProperties.getAppId();
        AccessTokenCache cache = ACCESS_TOKEN_CACHE.get(cacheKey);
        
        // 检查缓存是否有效
        if (cache != null && !cache.isExpired()) {
            return cache.getAccessToken();
        }
        
        // 从微信API获取新的Access Token
        String url = wechatProperties.getApiBaseUrl() + "/cgi-bin/token" +
                "?grant_type=client_credential" +
                "&appid=" + wechatProperties.getAppId() +
                "&secret=" + wechatProperties.getAppSecret();
        
        try {
            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
            
            JSONObject jsonObject = JSONUtil.parseObj(response);
            if (jsonObject.containsKey("access_token")) {
                String accessToken = jsonObject.getStr("access_token");
                LocalDateTime expireTime = LocalDateTime.now().plusSeconds(wechatProperties.getAccessTokenCacheTime());
                ACCESS_TOKEN_CACHE.put(cacheKey, new AccessTokenCache(accessToken, expireTime));
                log.info("获取微信Access Token成功");
                return accessToken;
            } else {
                String errorMsg = "获取微信Access Token失败: " + response;
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
        } catch (Exception e) {
            log.error("调用微信API获取Access Token异常", e);
            throw new RuntimeException("获取微信Access Token失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量生成小程序码
     */
    public List<QRCodeRespVO> generateQRCodes(QRCodeReqVO reqVO) {
        List<QRCodeRespVO> results = new ArrayList<>();
        String accessToken = getAccessToken();
        
        for (String clientToken : reqVO.getClientTokens()) {
            try {
                QRCodeRespVO result = generateSingleQRCode(accessToken, clientToken, reqVO);
                results.add(result);
            } catch (Exception e) {
                log.error("生成小程序码失败，clientToken: {}", clientToken, e);
                results.add(QRCodeRespVO.failed(clientToken, e.getMessage()));
            }
        }
        
        return results;
    }
    
    /**
     * 生成单个小程序码
     */
    private QRCodeRespVO generateSingleQRCode(String accessToken, String clientToken, QRCodeReqVO reqVO) {
        String url = wechatProperties.getApiBaseUrl() + "/wxa/getwxacode?access_token=" + accessToken;

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.set("path", "pages/more/binding?clientToken=" + clientToken);
        // requestBody.set("page", reqVO.getPage());
        // requestBody.set("scene", "clientToken=" + clientToken);
        requestBody.set("width", reqVO.getWidth());
        requestBody.set("check_path", reqVO.getCheckPath());
        requestBody.set("env_version", reqVO.getEnvVersion());

        try {
            byte[] imageBytes = webClient.post()
                    .uri(url)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody.toString())
                    .retrieve()
                    .bodyToMono(byte[].class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            
            if (imageBytes == null || imageBytes.length == 0) {
                throw new RuntimeException("获取到的图片数据为空");
            }
            
            // 检查返回的是否是错误信息（JSON格式）
            String responseStr = new String(imageBytes);
            if (responseStr.startsWith("{") && responseStr.contains("errcode")) {
                JSONObject errorObj = JSONUtil.parseObj(responseStr);
                String errorMsg = "微信API错误: " + errorObj.getStr("errmsg") + " (errcode: " + errorObj.getStr("errcode") + ")";
                throw new RuntimeException(errorMsg);
            }
            
            // 转换为Base64
            String base64Image = Base64.encode(imageBytes);

            // 如果需要使用模板合成，则进行图片合成
            if (reqVO.getUseTemplate() != null && reqVO.getUseTemplate()) {
                try {
                    base64Image = ImageComposer.composeQrCodeWithTemplate(base64Image, clientToken);
                    log.info("成功合成模板图片，clientToken: {}", clientToken);
                } catch (Exception e) {
                    log.warn("模板合成失败，使用原始小程序码，clientToken: {}, 错误: {}", clientToken, e.getMessage());
                }
            }

            String fullPath = reqVO.getPage() + "?clientToken=" + clientToken;

            log.info("成功生成小程序码，clientToken: {}, 图片大小: {} bytes", clientToken, imageBytes.length);
            return QRCodeRespVO.success(clientToken, base64Image, fullPath);
            
        } catch (Exception e) {
            log.error("调用微信API生成小程序码失败，clientToken: {}", clientToken, e);
            throw new RuntimeException("生成小程序码失败: " + e.getMessage());
        }
    }
}
