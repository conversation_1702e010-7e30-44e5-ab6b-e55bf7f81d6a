package com.quickdatax.demo.web.controller;

import com.quickdatax.demo.util.CommonResult;
import com.quickdatax.demo.web.controller.vo.QRCodeReqVO;
import com.quickdatax.demo.web.controller.vo.QRCodeRespVO;
import com.quickdatax.demo.web.service.WechatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 微信小程序相关接口
 * <AUTHOR>
 * @date 2024/07/04
 */
@Slf4j
@RestController
@RequestMapping("/pc-api/wechat")
@RequiredArgsConstructor
@Validated
public class WechatController {
    
    private final WechatService wechatService;
    
    /**
     * 批量生成小程序码
     * 
     * @param reqVO 请求参数
     * @return 生成结果列表
     */
    @PostMapping("/qrcode/batch")
    public CommonResult<List<QRCodeRespVO>> generateQRCodes(@Valid @RequestBody QRCodeReqVO reqVO) {
        log.info("开始批量生成小程序码，数量: {}", reqVO.getClientTokens().size());
        
        try {
            List<QRCodeRespVO> results = wechatService.generateQRCodes(reqVO);
            
            // 统计成功和失败数量
            long successCount = results.stream().filter(r -> "success".equals(r.getStatus())).count();
            long failedCount = results.size() - successCount;
            
            log.info("批量生成小程序码完成，成功: {}, 失败: {}", successCount, failedCount);
            
            return CommonResult.success(results);
        } catch (Exception e) {
            log.error("批量生成小程序码异常", e);
            return CommonResult.error(500, "生成小程序码失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量生成带模板的小程序码
     *
     * @param reqVO 请求参数
     * @return 生成结果列表
     */
    @PostMapping("/qrcode/batch-with-template")
    public CommonResult<List<QRCodeRespVO>> generateQRCodesWithTemplate(@Valid @RequestBody QRCodeReqVO reqVO) {
        log.info("开始批量生成带模板的小程序码，数量: {}", reqVO.getClientTokens().size());

        try {
            // 强制使用模板
            reqVO.setUseTemplate(true);

            List<QRCodeRespVO> results = wechatService.generateQRCodes(reqVO);

            // 统计成功和失败数量
            long successCount = results.stream().filter(r -> "success".equals(r.getStatus())).count();
            long failedCount = results.size() - successCount;

            log.info("批量生成带模板的小程序码完成，成功: {}, 失败: {}", successCount, failedCount);

            return CommonResult.success(results);
        } catch (Exception e) {
            log.error("批量生成带模板的小程序码异常", e);
            return CommonResult.error(500, "生成带模板的小程序码失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信Access Token（调试用）
     *
     * @return Access Token
     */
    @GetMapping("/access-token")
    public CommonResult<String> getAccessToken() {
        try {
            String accessToken = wechatService.getAccessToken();
            return CommonResult.success(accessToken);
        } catch (Exception e) {
            log.error("获取Access Token失败", e);
            return CommonResult.error(500, "获取Access Token失败: " + e.getMessage());
        }
    }
}
