package com.quickdatax.demo.web.controller.vo;

import lombok.Data;

/**
 * 小程序码生成响应VO
 * <AUTHOR>
 * @date 2024/07/04
 */
@Data
public class QRCodeRespVO {
    
    /**
     * 客户端Token
     */
    private String clientToken;
    
    /**
     * 小程序码Base64编码
     */
    private String qrCodeBase64;
    
    /**
     * 生成状态：success-成功，failed-失败
     */
    private String status;
    
    /**
     * 错误信息（失败时）
     */
    private String errorMessage;
    
    /**
     * 小程序页面完整路径（包含参数）
     */
    private String fullPath;
    
    public static QRCodeRespVO success(String clientToken, String qrCodeBase64, String fullPath) {
        QRCodeRespVO vo = new QRCodeRespVO();
        vo.setClientToken(clientToken);
        vo.setQrCodeBase64(qrCodeBase64);
        vo.setStatus("success");
        vo.setFullPath(fullPath);
        return vo;
    }
    
    public static QRCodeRespVO failed(String clientToken, String errorMessage) {
        QRCodeRespVO vo = new QRCodeRespVO();
        vo.setClientToken(clientToken);
        vo.setStatus("failed");
        vo.setErrorMessage(errorMessage);
        return vo;
    }
}
