package com.quickdatax.demo.web.controller.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 小程序码生成请求VO
 * <AUTHOR>
 * @date 2024/07/04
 */
@Data
public class QRCodeReqVO {
    
    /**
     * 客户端Token数组，用于生成多个小程序码
     */
    @NotEmpty(message = "clientTokens不能为空")
    @Size(max = 100, message = "一次最多生成100个小程序码")
    private List<String> clientTokens;
    
    /**
     * 小程序页面路径，默认为 pages/more/binding
     */
    private String page = "pages/more/binding";
    
    /**
     * 二维码宽度，默认430px
     */
    private Integer width = 430;
    
    /**
     * 是否检查页面路径，默认true
     */
    private Boolean checkPath = true;
    
    /**
     * 小程序版本，release-正式版，trial-体验版，develop-开发版
     */
    private String envVersion = "release";

    /**
     * 是否使用模板合成，默认false
     */
    private Boolean useTemplate = false;
}
