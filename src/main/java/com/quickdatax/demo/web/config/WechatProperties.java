package com.quickdatax.demo.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序配置
 * <AUTHOR>
 * @date 2024/07/04
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "quickdatax.wechat")
public class WechatProperties {
    
    /**
     * 小程序AppID
     */
    private String appId;
    
    /**
     * 小程序AppSecret
     */
    private String appSecret;
    
    /**
     * 微信API基础URL
     */
    private String apiBaseUrl = "https://api.weixin.qq.com";
    
    /**
     * Access Token缓存时间（秒），默认7000秒（微信官方是7200秒，提前200秒刷新）
     */
    private Long accessTokenCacheTime = 7000L;
}
