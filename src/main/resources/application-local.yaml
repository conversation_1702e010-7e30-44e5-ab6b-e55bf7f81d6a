spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **************************************************************
    username: postgres
    password: test_123

quickdatax:
  emqx:
    broker: tcp://*************:2883
    user-name: QDX_SUPER
    password: 12345678
    client-id: local_demo
    api-host: *************:18083
    api-key: 3d78b72d404be139
    api-secret-key: i2QF09CCqzmw5jgZm2aSe33jAHlRL4tHauajR72MjHUK
  wechat:
    app-id: wx0afae3b4d700edce
    app-secret: your_miniprogram_appsecret_here
    api-base-url: https://api.weixin.qq.com
    access-token-cache-time: 7000
  influxdb2:
    host: http://*************:8089
    org: quickdatax
    bucket: iot
    #    token: XitaQQpCvcTcwmNQW8qWImQINIFGbozDzsPb-UNUPz5KkzPqqoZJUv_CNG_fGh3rTXJkafOUI18kJ-m6KQcsiQ==  本地
    token: uCrRgN_rwuaEn331lzzIB-0Sm2uI3hnQ6mu46wl8nF5ZXy8FuHfwQV_sUbV7I0uN40pjTDyBDZD3XoubAevrew==  # 184
    # 源数据客户端参数
    ori-org: quickdatax
    ori-bucket: iot
    ori-host: http://*************:8089/
    ori-token: WEQE5eHK1R8Je12T4vK84Jz9zCGFQs1EQrPjkThtVjkSFVAVvHrplZbjzSzdSeuYD39dB-w36ssK3ffbLUnTFw==