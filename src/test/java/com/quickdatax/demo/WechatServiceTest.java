package com.quickdatax.demo;

import com.quickdatax.demo.web.controller.vo.QRCodeReqVO;
import com.quickdatax.demo.web.controller.vo.QRCodeRespVO;
import com.quickdatax.demo.web.service.WechatService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 微信服务测试类
 * 注意：运行此测试前需要在application-local.yaml中配置正确的微信小程序AppID和AppSecret
 */
@SpringBootTest
@ActiveProfiles("local")
public class WechatServiceTest {
    
    @Resource
    private WechatService wechatService;
    
    @Test
    public void testGetAccessToken() {
        try {
            String accessToken = wechatService.getAccessToken();
            System.out.println("Access Token: " + accessToken);
            assert accessToken != null && !accessToken.isEmpty();
        } catch (Exception e) {
            System.err.println("获取Access Token失败，请检查微信配置: " + e.getMessage());
        }
    }
    
    @Test
    public void testGenerateQRCodes() {
        try {
            QRCodeReqVO reqVO = new QRCodeReqVO();
            reqVO.setClientTokens(Arrays.asList("test_token_1", "test_token_2", "test_token_3"));
            reqVO.setPage("pages/more/binding");
            reqVO.setWidth(430);
            reqVO.setEnvVersion("release");
            
            List<QRCodeRespVO> results = wechatService.generateQRCodes(reqVO);
            
            System.out.println("生成结果数量: " + results.size());
            for (QRCodeRespVO result : results) {
                System.out.println("Token: " + result.getClientToken() + 
                                 ", Status: " + result.getStatus() + 
                                 ", Path: " + result.getFullPath());
                if ("success".equals(result.getStatus())) {
                    System.out.println("Base64长度: " + result.getQrCodeBase64().length());
                } else {
                    System.out.println("错误信息: " + result.getErrorMessage());
                }
            }
            
            assert !results.isEmpty();
        } catch (Exception e) {
            System.err.println("生成小程序码失败，请检查微信配置: " + e.getMessage());
        }
    }
}
