package com.quickdatax.demo;

import com.quickdatax.demo.util.ImageComposer;
import com.quickdatax.demo.util.QRCodeUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.FileOutputStream;
import java.util.Base64;

/**
 * 图片合成测试类
 */
@SpringBootTest
@ActiveProfiles("local")
public class ImageComposerTest {
    
    @Test
    public void testImageComposer() {
        try {
            // 生成一个测试二维码
            String qrContent = "https://example.com/test";
            String qrCodeBase64 = QRCodeUtil.generateQrCodeBase64(qrContent, 200, 200);
            
            // 测试图片合成
            String clientToken = "TEST_TOKEN_123";
            String composedImageBase64 = ImageComposer.composeQrCodeWithTemplate(qrCodeBase64, clientToken);
            
            // 保存合成后的图片到文件（用于验证效果）
            byte[] imageBytes = Base64.getDecoder().decode(composedImageBase64);
            try (FileOutputStream fos = new FileOutputStream("test_composed_image.png")) {
                fos.write(imageBytes);
                System.out.println("合成图片已保存到: test_composed_image.png");
            }
            
            System.out.println("图片合成测试成功！");
            System.out.println("clientToken: " + clientToken);
            System.out.println("合成图片Base64长度: " + composedImageBase64.length());
            
        } catch (Exception e) {
            System.err.println("图片合成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testMultipleTokens() {
        try {
            String[] testTokens = {
                "USER_001",
                "CLIENT_ABC123",
                "DEVICE_XYZ789",
                "VERY_LONG_TOKEN_NAME_TEST_123456"
            };
            
            for (String token : testTokens) {
                // 生成测试二维码
                String qrContent = "pages/more/binding?clientToken=" + token;
                String qrCodeBase64 = QRCodeUtil.generateQrCodeBase64(qrContent, 200, 200);
                
                // 合成图片
                String composedImageBase64 = ImageComposer.composeQrCodeWithTemplate(qrCodeBase64, token);
                
                // 保存图片
                byte[] imageBytes = Base64.getDecoder().decode(composedImageBase64);
                String filename = "test_" + token.replaceAll("[^a-zA-Z0-9]", "_") + ".png";
                try (FileOutputStream fos = new FileOutputStream(filename)) {
                    fos.write(imageBytes);
                    System.out.println("保存图片: " + filename);
                }
            }
            
            System.out.println("多Token测试完成！");
            
        } catch (Exception e) {
            System.err.println("多Token测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
