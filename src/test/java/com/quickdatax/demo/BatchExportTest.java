package com.quickdatax.demo;

import com.quickdatax.demo.util.BatchExportUtil;
import com.quickdatax.demo.util.QRCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量导出功能测试
 * <AUTHOR>
 * @date 2024/07/04
 */
@Slf4j
@SpringBootTest
public class BatchExportTest {
    
    @Test
    public void testBatchExport() {
        try {
            log.info("开始测试批量导出功能...");
            
            // 1. 生成测试用的二维码数据
            List<BatchExportUtil.QRCodeData> qrCodeDataList = generateTestQRCodes();
            
            // 2. 创建ZIP文件
            String zipFileName = "test_batch_export.zip";
            ResponseEntity<byte[]> response = BatchExportUtil.createQRCodeZip(qrCodeDataList, zipFileName);
            
            // 3. 验证响应
            assert response.getStatusCode().is2xxSuccessful();
            assert response.getBody() != null;
            assert response.getBody().length > 0;
            
            // 4. 保存ZIP文件到本地（用于手动验证）
            saveZipFile(response.getBody(), zipFileName);
            
            log.info("批量导出测试完成！ZIP文件大小: {} bytes", response.getBody().length);
            log.info("ZIP文件已保存到: {}", zipFileName);
            
        } catch (Exception e) {
            log.error("批量导出测试失败", e);
            throw new RuntimeException(e);
        }
    }
    
    @Test
    public void testBatchExportWithTemplate() {
        try {
            log.info("开始测试带模板的批量导出功能...");
            
            // 1. 生成带模板的测试二维码数据
            List<BatchExportUtil.QRCodeData> qrCodeDataList = generateTemplateQRCodes();
            
            // 2. 创建ZIP文件
            String zipFileName = "test_template_batch_export.zip";
            ResponseEntity<byte[]> response = BatchExportUtil.createQRCodeZip(qrCodeDataList, zipFileName);
            
            // 3. 验证响应
            assert response.getStatusCode().is2xxSuccessful();
            assert response.getBody() != null;
            assert response.getBody().length > 0;
            
            // 4. 保存ZIP文件
            saveZipFile(response.getBody(), zipFileName);
            
            log.info("带模板的批量导出测试完成！ZIP文件大小: {} bytes", response.getBody().length);
            log.info("ZIP文件已保存到: {}", zipFileName);
            
        } catch (Exception e) {
            log.error("带模板的批量导出测试失败", e);
            throw new RuntimeException(e);
        }
    }
    
    @Test
    public void testEmptyQRCodeList() {
        try {
            log.info("测试空列表情况...");
            
            List<BatchExportUtil.QRCodeData> emptyList = new ArrayList<>();
            ResponseEntity<byte[]> response = BatchExportUtil.createQRCodeZip(emptyList, "empty_test.zip");
            
            // 空列表也应该能创建ZIP文件（只是没有内容）
            assert response.getStatusCode().is2xxSuccessful();
            assert response.getBody() != null;
            
            log.info("空列表测试完成！");
            
        } catch (Exception e) {
            log.error("空列表测试失败", e);
            throw new RuntimeException(e);
        }
    }
    
    @Test
    public void testInvalidBase64Data() {
        try {
            log.info("测试无效Base64数据处理...");
            
            List<BatchExportUtil.QRCodeData> qrCodeDataList = new ArrayList<>();
            
            // 添加有效数据
            String validQRCode = QRCodeUtil.generateQrCodeBase64("test_content", 200, 200);
            qrCodeDataList.add(new BatchExportUtil.QRCodeData("VALID_TOKEN", validQRCode));
            
            // 添加无效数据
            qrCodeDataList.add(new BatchExportUtil.QRCodeData("INVALID_TOKEN", "invalid_base64_data"));
            
            // 应该能处理混合数据，跳过无效的
            ResponseEntity<byte[]> response = BatchExportUtil.createQRCodeZip(qrCodeDataList, "mixed_test.zip");
            
            assert response.getStatusCode().is2xxSuccessful();
            assert response.getBody() != null;
            
            log.info("无效数据处理测试完成！");
            
        } catch (Exception e) {
            log.error("无效数据处理测试失败", e);
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 生成测试用的二维码数据
     */
    private List<BatchExportUtil.QRCodeData> generateTestQRCodes() {
        List<BatchExportUtil.QRCodeData> qrCodeDataList = new ArrayList<>();
        
        String[] testTokens = {"TEST_001", "TEST_002", "TEST_003", "USER_ABC", "DEVICE_XYZ"};
        
        for (String token : testTokens) {
            try {
                String content = "pages/more/binding?clientToken=" + token;
                String base64QRCode = QRCodeUtil.generateQrCodeBase64(content, 200, 200);
                qrCodeDataList.add(new BatchExportUtil.QRCodeData(token, base64QRCode));
                
                log.debug("生成测试二维码: {}", token);
                
            } catch (Exception e) {
                log.warn("生成测试二维码失败: {}", token, e);
            }
        }
        
        return qrCodeDataList;
    }
    
    /**
     * 生成带模板的测试二维码数据
     */
    private List<BatchExportUtil.QRCodeData> generateTemplateQRCodes() {
        List<BatchExportUtil.QRCodeData> qrCodeDataList = new ArrayList<>();
        
        String[] testTokens = {"TEMPLATE_001", "TEMPLATE_002", "TEMPLATE_003"};
        
        for (String token : testTokens) {
            try {
                // 先生成原始二维码
                String content = "pages/more/binding?clientToken=" + token;
                String originalQRCode = QRCodeUtil.generateQrCodeBase64(content, 200, 200);
                
                // 合成到模板
                String composedQRCode = QRCodeUtil.composeQrCodeWithTemplate(originalQRCode, token);
                qrCodeDataList.add(new BatchExportUtil.QRCodeData(token, composedQRCode));
                
                log.debug("生成模板二维码: {}", token);
                
            } catch (Exception e) {
                log.warn("生成模板二维码失败: {}", token, e);
            }
        }
        
        return qrCodeDataList;
    }
    
    /**
     * 保存ZIP文件到本地
     */
    private void saveZipFile(byte[] zipData, String fileName) {
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(zipData);
            log.info("ZIP文件已保存: {}", fileName);
        } catch (IOException e) {
            log.error("保存ZIP文件失败: {}", fileName, e);
        }
    }
}
