# 二维码网格打印功能

## 功能概述

新增的二维码网格打印功能可以将多个模板二维码按照规整的网格格式合并成一张大图，专门用于打印后剪裁贴到设备上。

## 接口信息

### 接口地址
```
POST /pc-api/wechat/qrcode/grid-template
```

### 请求参数
```json
{
    "clientTokens": ["DEVICE_001", "DEVICE_002", "DEVICE_003", ...],
    "page": "pages/more/binding",
    "useTemplate": true
}
```

### 响应格式
```json
{
    "code": 200,
    "msg": "success",
    "data": "iVBORw0KGgoAAAANSUhEUgAA..." // Base64编码的网格图片
}
```

## 网格规格

- **网格布局**: 每行5个，每列15个
- **最大容量**: 75个二维码（5×15）
- **单个尺寸**: 200×200像素
- **图片间距**: 20像素
- **页面边距**: 40像素
- **总图尺寸**: 约1140×3040像素

## 打印建议

### 纸张设置
- **推荐纸张**: A4纸（210×297mm）
- **打印方向**: 纵向
- **缩放设置**: 建议缩放至65-70%以适应A4纸张

### 打印质量
- **分辨率**: 建议300DPI以上
- **颜色模式**: 黑白打印即可
- **纸张类型**: 普通复印纸或标签纸

## 剪裁说明

### 网格线
- 图片包含浅灰色虚线网格，方便定位剪裁位置
- 网格线不会影响二维码的扫描功能

### 剪裁尺寸
- 每个二维码区域: 约200×200像素
- 实际打印尺寸取决于缩放比例
- 建议保留少量边距以便粘贴

## 使用流程

1. **准备Token列表**: 整理需要生成二维码的设备Token
2. **调用接口**: 发送POST请求到网格生成接口
3. **下载图片**: 保存返回的Base64图片
4. **打印**: 使用合适的打印设置打印图片
5. **剪裁**: 沿着网格线剪裁出单个二维码
6. **粘贴**: 将二维码贴到对应的设备上

## 测试页面

提供了两个测试页面：

### 1. grid_test.html
专门的网格生成测试页面，包含：
- Token输入界面
- 实时预览功能
- 下载功能
- 使用说明

### 2. test_page.html
综合测试页面，新增了网格生成测试部分

## 技术实现

### 后端实现
- `QRCodeGridComposer`: 网格合并工具类
- `WechatController.generateQRCodeGrid()`: 网格生成接口

### 核心功能
1. **二维码生成**: 复用现有的模板二维码生成逻辑
2. **图片合并**: 使用Java BufferedImage进行图片合并
3. **网格布局**: 精确计算每个二维码的位置
4. **网格线绘制**: 添加辅助剪裁线
5. **Base64编码**: 返回可直接使用的图片数据

## 错误处理

- **数量限制**: 最多75个二维码
- **生成失败**: 自动过滤失败的二维码
- **空结果**: 检查是否有成功生成的二维码
- **异常处理**: 完整的错误信息返回

## 示例代码

### JavaScript调用示例
```javascript
async function generateGrid(clientTokens) {
    const response = await fetch('/pc-api/wechat/qrcode/grid-template', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            clientTokens: clientTokens,
            page: 'pages/more/binding',
            useTemplate: true
        })
    });
    
    const result = await response.json();
    if (result.code === 200) {
        // 显示或下载图片
        const img = document.createElement('img');
        img.src = `data:image/png;base64,${result.data}`;
        document.body.appendChild(img);
    }
}
```

### 下载图片示例
```javascript
function downloadGrid(base64Data) {
    const link = document.createElement('a');
    link.href = `data:image/png;base64,${base64Data}`;
    link.download = `qrcode_grid_${new Date().getTime()}.png`;
    link.click();
}
```

## 注意事项

1. **Token格式**: 确保Token格式正确，避免特殊字符
2. **数量控制**: 单次最多75个，超出会返回错误
3. **网络超时**: 大量二维码生成可能需要较长时间
4. **图片大小**: 生成的图片较大，注意网络传输
5. **打印测试**: 建议先打印一小部分测试效果

## 扩展功能

未来可以考虑的扩展：
- 自定义网格布局（行列数）
- 不同的二维码尺寸选项
- 多页面支持（超过75个时分页）
- PDF格式输出
- 批量文件名生成
