# 微信小程序码模板合成使用指南

## 功能概述

本功能可以将生成的微信小程序码自动合成到绿色模板图片上，实现以下效果：

1. **二维码放置** - 将小程序码精确放置在模板左侧的空白矩形区域
2. **文字添加** - 在"ID:"右侧自动添加对应的clientToken
3. **样式统一** - 保持与模板原有文字相同的字体、大小和颜色

## 使用方法

### 1. API调用方式

#### 方式一：使用专用端点（推荐）

```bash
curl -X POST http://localhost:47081/pc-api/wechat/qrcode/batch-with-template \
  -H "Content-Type: application/json" \
  -d '{
    "clientTokens": ["USER_001", "USER_002"],
    "page": "pages/more/binding",
    "width": 430,
    "envVersion": "release"
  }'
```

#### 方式二：使用通用端点 + 参数

```bash
curl -X POST http://localhost:8080/pc-api/wechat/qrcode/batch \
  -H "Content-Type: application/json" \
  -d '{
    "clientTokens": ["USER_001", "USER_002"],
    "page": "pages/more/binding",
    "width": 430,
    "envVersion": "release",
    "useTemplate": true
  }'
```

### 2. 前端页面使用

1. 打开 `frontend_example.html`
2. 勾选"使用绿色模板合成"选项
3. 输入客户端Token列表
4. 点击"生成小程序码"

## 模板配置

### 默认模板参数

如果没有提供模板图片，系统会自动生成一个默认的绿色模板：

```java
// 模板尺寸
宽度: 400px
高度: 165px
背景色: #2ECC71 (绿色)
圆角: 20px

// 二维码区域
位置: (25, 25)
尺寸: 115x115px
背景: 白色

// 文字区域
"ID:" 位置: (180, 120)
字体: Microsoft YaHei, 粗体, 24px
颜色: 白色
```

### 自定义模板

1. 将您的模板图片保存为 `src/main/resources/static/template.png`
2. 根据实际模板调整 `ImageComposer.java` 中的参数：

```java
// 二维码区域配置
private static final int QR_CODE_X = 25;      // 调整X坐标
private static final int QR_CODE_Y = 25;      // 调整Y坐标
private static final int QR_CODE_WIDTH = 115; // 调整宽度
private static final int QR_CODE_HEIGHT = 115; // 调整高度

// 文字配置
private static final int TEXT_X = 180;        // 调整"ID:"X坐标
private static final int TEXT_Y = 120;        // 调整"ID:"Y坐标
private static final int FONT_SIZE = 24;      // 调整字体大小
```

## 技术实现

### 核心类说明

1. **ImageComposer** - 图片合成核心类
   - 负责加载模板图片
   - 处理二维码图片解码
   - 执行图片合成操作
   - 绘制clientToken文字

2. **QRCodeReqVO** - 请求参数
   - 新增 `useTemplate` 字段控制是否使用模板

3. **WechatService** - 业务逻辑
   - 集成模板合成功能
   - 异常处理和降级策略

### 图片合成流程

```mermaid
graph TD
    A[接收小程序码Base64] --> B[加载模板图片]
    B --> C[解码小程序码图片]
    C --> D[创建合成画布]
    D --> E[绘制模板背景]
    E --> F[绘制小程序码]
    F --> G[绘制clientToken文字]
    G --> H[输出合成图片Base64]
```

## 测试验证

### 运行测试

```bash
# 运行图片合成测试
mvn test -Dtest=ImageComposerTest

# 测试完成后会在项目根目录生成测试图片
ls test_*.png
```

### 手动测试

1. 启动应用
2. 使用Postman或curl调用API
3. 将返回的Base64图片保存为文件验证效果

## 注意事项

### 1. 性能考虑

- 图片合成会增加处理时间（约100-200ms每张）
- 建议批量处理时控制并发数量
- 大批量生成时考虑异步处理

### 2. 内存使用

- 每张合成图片约占用50-100KB内存
- 批量处理时注意内存限制
- 及时释放图片资源

### 3. 错误处理

- 模板图片加载失败时自动使用默认模板
- 合成失败时降级返回原始小程序码
- 详细错误日志便于问题排查

### 4. 文字长度限制

- clientToken过长时自动截断并添加"..."
- 最大显示宽度180像素
- 保证文字不会超出模板边界

## 常见问题

### Q: 如何调整二维码在模板中的位置？

A: 修改 `ImageComposer.java` 中的 `QR_CODE_X`、`QR_CODE_Y`、`QR_CODE_WIDTH`、`QR_CODE_HEIGHT` 常量。

### Q: 如何更改文字样式？

A: 修改 `ImageComposer.java` 中的 `FONT_NAME`、`FONT_SIZE`、`TEXT_COLOR` 常量。

### Q: 模板图片格式有什么要求？

A: 支持PNG、JPG等常见格式，建议使用PNG格式以保证透明度支持。

### Q: 如何批量生成不同模板的小程序码？

A: 可以扩展 `ImageComposer` 类，支持动态模板选择，或者创建多个模板配置。

## 扩展功能

### 可能的扩展方向

1. **多模板支持** - 支持多种模板样式选择
2. **动态文字** - 支持更多动态文字字段
3. **样式自定义** - 支持运行时调整样式参数
4. **批量下载** - 支持ZIP打包下载
5. **缓存优化** - 缓存合成结果提高性能

### 示例扩展代码

```java
// 多模板支持示例
public static String composeWithTemplate(String qrCodeBase64, String clientToken, String templateName) {
    String templatePath = "static/templates/" + templateName + ".png";
    // ... 实现逻辑
}

// 动态样式支持示例
public static String composeWithStyle(String qrCodeBase64, String clientToken, StyleConfig style) {
    // ... 使用自定义样式配置
}
```

---

**开发者:** freeze  
**日期:** 2024-07-04  
**版本:** v1.0
