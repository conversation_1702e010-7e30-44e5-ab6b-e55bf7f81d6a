<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码网格打印测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        textarea {
            width: 100%;
            min-height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            resize: vertical;
        }
        button {
            background-color: #07c160;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #06ad56;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .message {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid-image {
            text-align: center;
            margin: 20px 0;
        }
        .grid-image img {
            max-width: 100%;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .download-section {
            text-align: center;
            margin: 20px 0;
        }
        .download-btn {
            background-color: #28a745;
            font-size: 18px;
            padding: 15px 30px;
        }
        .download-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>二维码网格打印生成器</h1>
        <p>生成规整的二维码网格图片，方便打印后剪裁贴到设备上</p>
        
        <div class="info-box">
            <h4>📋 使用说明</h4>
            <ul>
                <li><strong>网格格式：</strong>每行5个，每列15个，最多75个二维码</li>
                <li><strong>图片尺寸：</strong>每个二维码200×200像素，总图片约1140×3040像素</li>
                <li><strong>打印建议：</strong>使用A4纸打印，建议设置为实际尺寸或缩放至合适大小</li>
                <li><strong>剪裁参考：</strong>图片包含浅色网格线，方便剪裁定位</li>
            </ul>
        </div>
        
        <div class="form-section">
            <h3>输入客户端Token</h3>
            <div class="form-group">
                <label for="clientTokens">客户端Token列表（每行一个，最多75个）：</label>
                <textarea id="clientTokens" placeholder="请输入客户端Token，每行一个，例如：&#10;DEVICE_001&#10;DEVICE_002&#10;DEVICE_003&#10;..."></textarea>
            </div>
            
            <button onclick="generateGrid()" id="generateBtn">🎯 生成网格图片</button>
            <button onclick="fillExample()">📝 填充示例数据</button>
            <button onclick="clearAll()">🗑️ 清空所有</button>
        </div>
        
        <div id="messageArea"></div>
        
        <div class="result-section" id="resultSection" style="display: none;">
            <h3>生成结果</h3>
            <div id="gridImageArea" class="grid-image"></div>
            <div class="download-section">
                <button onclick="downloadImage()" class="download-btn" id="downloadBtn">
                    💾 下载网格图片
                </button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:47081';
        let currentGridImage = null;

        // 生成网格图片
        async function generateGrid() {
            const clientTokensText = document.getElementById('clientTokens').value.trim();
            const messageArea = document.getElementById('messageArea');
            const resultSection = document.getElementById('resultSection');
            const generateBtn = document.getElementById('generateBtn');
            
            // 验证输入
            if (!clientTokensText) {
                showMessage('请输入客户端Token', 'error');
                return;
            }
            
            const clientTokens = clientTokensText.split('\n')
                .map(token => token.trim())
                .filter(token => token.length > 0);
            
            if (clientTokens.length === 0) {
                showMessage('请输入有效的客户端Token', 'error');
                return;
            }
            
            if (clientTokens.length > 75) {
                showMessage('Token数量超过限制，最多支持75个（15行×5列）', 'error');
                return;
            }
            
            try {
                generateBtn.disabled = true;
                generateBtn.textContent = '🔄 生成中...';
                resultSection.style.display = 'none';
                
                showMessage(`正在生成${clientTokens.length}个二维码的网格图片，请稍候...`, 'loading');
                
                const requestData = {
                    clientTokens: clientTokens,
                    page: 'pages/more/binding',
                    useTemplate: true
                };
                
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/qrcode/grid-template`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.code === 200) {
                    currentGridImage = result.data;
                    displayGridImage(result.data);
                    showMessage('✅ 网格图片生成成功！可以下载打印了', 'success');
                    resultSection.style.display = 'block';
                } else {
                    showMessage(`❌ 生成失败：${result.msg || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                console.error('生成网格图片失败:', error);
                showMessage(`❌ 网络错误：${error.message}`, 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '🎯 生成网格图片';
            }
        }
        
        // 显示网格图片
        function displayGridImage(base64Data) {
            const gridImageArea = document.getElementById('gridImageArea');
            gridImageArea.innerHTML = `
                <img src="data:image/png;base64,${base64Data}" 
                     alt="二维码网格图片" 
                     style="max-width: 100%; border: 2px solid #ddd; border-radius: 8px;"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                <div style="display: none; color: #e74c3c; padding: 20px; border: 1px solid #e74c3c; border-radius: 4px;">
                    图片加载失败
                </div>
                <p style="color: #666; margin-top: 10px;">
                    📐 图片包含网格线，方便剪裁定位<br>
                    🖨️ 建议使用A4纸打印，设置为实际尺寸
                </p>
            `;
        }
        
        // 下载图片
        function downloadImage() {
            if (!currentGridImage) {
                showMessage('❌ 没有可下载的图片', 'error');
                return;
            }
            
            try {
                const link = document.createElement('a');
                link.href = `data:image/png;base64,${currentGridImage}`;
                link.download = `qrcode_grid_${new Date().getTime()}.png`;
                link.click();
                showMessage('✅ 图片下载成功', 'success');
            } catch (error) {
                showMessage('❌ 下载失败: ' + error.message, 'error');
            }
        }
        
        // 填充示例数据
        function fillExample() {
            const examples = [];
            for (let i = 1; i <= 20; i++) {
                examples.push(`DEVICE_${String(i).padStart(3, '0')}`);
            }
            document.getElementById('clientTokens').value = examples.join('\n');
        }
        
        // 清空所有
        function clearAll() {
            document.getElementById('clientTokens').value = '';
            document.getElementById('messageArea').innerHTML = '';
            document.getElementById('resultSection').style.display = 'none';
            currentGridImage = null;
        }
        
        // 显示消息
        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `<div class="message ${type}">${message}</div>`;
            
            // 成功消息3秒后自动消失
            if (type === 'success') {
                setTimeout(() => {
                    messageArea.innerHTML = '';
                }, 3000);
            }
        }
        
        // 页面加载时检查API连接
        window.addEventListener('load', async function() {
            try {
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/access-token`);
                if (response.ok) {
                    showMessage('🟢 服务器连接正常', 'success');
                } else {
                    showMessage('🔴 服务器连接异常，请检查服务是否启动', 'error');
                }
            } catch (error) {
                showMessage('🔴 无法连接到服务器，请检查服务是否启动在端口47081', 'error');
            }
        });
    </script>
</body>
</html>
