# 微信小程序码生成API使用说明

## 功能概述

本API实现了微信小程序码的批量生成功能，支持：
- 根据输入的字符串数组批量生成小程序码
- 用户扫描后跳转到指定页面并携带参数
- 返回Base64编码的小程序码图片

## 配置说明

### 1. 修改配置文件

在 `src/main/resources/application-local.yaml` 中配置您的微信小程序信息：

```yaml
quickdatax:
  wechat:
    app-id: wx1234567890abcdef  # 替换为您的小程序AppID
    app-secret: your_app_secret_here  # 替换为您的小程序AppSecret
    api-base-url: https://api.weixin.qq.com
    access-token-cache-time: 7000
```

### 2. 获取小程序AppID和AppSecret

1. 登录微信公众平台：https://mp.weixin.qq.com/
2. 进入您的小程序管理后台
3. 在"开发" -> "开发管理" -> "开发设置"中找到AppID和AppSecret

## API接口说明

### 1. 批量生成小程序码

**接口地址：** `POST /pc-api/wechat/qrcode/batch`

**请求参数：**
```json
{
  "clientTokens": ["token1", "token2", "token3"],
  "page": "pages/more/binding",
  "width": 430,
  "checkPath": true,
  "envVersion": "release"
}
```

**参数说明：**
- `clientTokens`: 字符串数组，每个字符串会作为clientToken参数传递给小程序
- `page`: 小程序页面路径，默认为"pages/more/binding"
- `width`: 二维码宽度，默认430px
- `checkPath`: 是否检查页面路径，默认true
- `envVersion`: 小程序版本，release-正式版，trial-体验版，develop-开发版

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "clientToken": "token1",
      "qrCodeBase64": "iVBORw0KGgoAAAANSUhEUgAA...",
      "status": "success",
      "fullPath": "pages/more/binding?clientToken=token1"
    },
    {
      "clientToken": "token2",
      "qrCodeBase64": "iVBORw0KGgoAAAANSUhEUgAA...",
      "status": "success",
      "fullPath": "pages/more/binding?clientToken=token2"
    }
  ],
  "msg": ""
}
```

### 2. 获取Access Token（调试用）

**接口地址：** `GET /pc-api/wechat/access-token`

**响应示例：**
```json
{
  "code": 200,
  "data": "ACCESS_TOKEN_STRING",
  "msg": ""
}
```

## 小程序端获取参数

用户扫描小程序码后，在小程序的目标页面可以通过以下方式获取clientToken参数：

```javascript
Page({
  onLoad(query) {
    // 获取clientToken参数
    const clientToken = query.clientToken;
    console.log('客户端Token:', clientToken);
    
    // 进行后续业务逻辑处理
    this.handleBinding(clientToken);
  },
  
  handleBinding(clientToken) {
    // 处理绑定逻辑
    // ...
  }
})
```

## 使用示例

### 1. 使用curl测试

```bash
curl -X POST http://localhost:8080/pc-api/wechat/qrcode/batch \
  -H "Content-Type: application/json" \
  -d '{
    "clientTokens": ["user123", "user456", "user789"],
    "page": "pages/more/binding",
    "width": 430,
    "envVersion": "release"
  }'
```

### 2. 使用JavaScript

```javascript
const generateQRCodes = async (tokens) => {
  const response = await fetch('/pc-api/wechat/qrcode/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      clientTokens: tokens,
      page: 'pages/more/binding',
      width: 430,
      envVersion: 'release'
    })
  });
  
  const result = await response.json();
  
  if (result.code === 200) {
    result.data.forEach(item => {
      if (item.status === 'success') {
        // 显示二维码图片
        const img = document.createElement('img');
        img.src = 'data:image/png;base64,' + item.qrCodeBase64;
        document.body.appendChild(img);
      } else {
        console.error('生成失败:', item.errorMessage);
      }
    });
  }
};

// 使用示例
generateQRCodes(['token1', 'token2', 'token3']);
```

## 注意事项

1. **频率限制：** 微信API有调用频率限制（5000次/分钟），请合理控制调用频率
2. **参数限制：** scene参数最大32个可见字符，只支持数字、大小写英文及部分特殊字符
3. **页面路径：** 确保配置的页面路径在小程序中存在
4. **Access Token缓存：** 系统会自动缓存Access Token，有效期7000秒
5. **错误处理：** 请根据返回的status字段判断每个二维码的生成状态

## 错误码说明

常见的微信API错误码：
- 40001: access_token无效
- 40129: scene参数无效
- 41030: page路径不正确
- 85096: 不允许包含scancode_time字段
- 40097: 参数错误
- 40169: scene长度不合法

## 技术实现

本实现基于微信官方文档的"获取不限制的小程序码"接口：
https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getUnlimitedQRCode.html

主要特性：
- 使用Spring WebFlux的WebClient进行HTTP调用
- 实现Access Token自动获取和缓存
- 支持批量生成，提高效率
- 完整的错误处理和日志记录
- 返回Base64编码的图片，便于前端使用
