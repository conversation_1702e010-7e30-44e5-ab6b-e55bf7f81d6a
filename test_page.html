<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序码功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #07c160;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #06ad56;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            min-height: 20px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序码功能测试</h1>
        <p>此页面用于测试微信小程序码生成和批量导出功能</p>
        
        <!-- 服务器连接状态 -->
        <div class="test-section">
            <h3><span id="statusIndicator" class="status-indicator status-offline"></span>服务器连接状态</h3>
            <button onclick="checkConnection()">检查连接</button>
            <div id="connectionResult" class="result"></div>
        </div>
        
        <!-- Access Token测试 -->
        <div class="test-section">
            <h3>微信Access Token测试</h3>
            <button onclick="testAccessToken()">获取Access Token</button>
            <div id="tokenResult" class="result"></div>
        </div>
        
        <!-- 普通二维码生成测试 -->
        <div class="test-section">
            <h3>普通二维码生成测试</h3>
            <button onclick="testNormalQRCode()">生成普通二维码</button>
            <div id="normalResult" class="result"></div>
        </div>
        
        <!-- 模板二维码生成测试 -->
        <div class="test-section">
            <h3>模板二维码生成测试</h3>
            <button onclick="testTemplateQRCode()">生成模板二维码</button>
            <div id="templateResult" class="result"></div>

            <!-- 图片显示表格 -->
            <div id="templateImagesTable" style="display: none; margin-top: 20px;">
                <h4>生成的模板二维码图片：</h4>
                <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Token</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">二维码图片</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">状态</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="templateImagesBody">
                        <!-- 动态生成的图片行 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 网格图片生成测试 -->
        <div class="test-section">
            <h3>网格图片生成测试（打印用）</h3>
            <button onclick="testGridGeneration()">生成网格图片</button>
            <div id="gridResult" class="result"></div>
            <div id="gridImageDisplay" style="display: none; margin-top: 20px; text-align: center;">
                <h4>生成的网格图片：</h4>
                <div id="gridImageContainer"></div>
                <button onclick="downloadGridImage()" style="background-color: #28a745; margin-top: 10px;">下载网格图片</button>
            </div>
        </div>

        <!-- 批量导出测试 -->
        <div class="test-section">
            <h3>批量导出测试</h3>
            <button onclick="testBatchExport()" id="exportBtn" disabled>测试批量导出</button>
            <div id="exportResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:47081';
        let testQRCodes = [];

        // 检查服务器连接
        async function checkConnection() {
            const resultDiv = document.getElementById('connectionResult');
            const indicator = document.getElementById('statusIndicator');
            
            resultDiv.innerHTML = '正在检查连接...';
            resultDiv.className = 'result loading';
            
            try {
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/access-token`);
                if (response.ok) {
                    resultDiv.innerHTML = '✅ 服务器连接正常';
                    resultDiv.className = 'result success';
                    indicator.className = 'status-indicator status-online';
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 连接失败: ${error.message}`;
                resultDiv.className = 'result error';
                indicator.className = 'status-indicator status-offline';
                return false;
            }
        }

        // 测试Access Token
        async function testAccessToken() {
            const resultDiv = document.getElementById('tokenResult');
            
            resultDiv.innerHTML = '正在获取Access Token...';
            resultDiv.className = 'result loading';
            
            try {
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/access-token`);
                const result = await response.json();
                
                if (result.code === 200) {
                    resultDiv.innerHTML = `✅ Access Token获取成功: ${result.data.substring(0, 20)}...`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `❌ 获取失败: ${result.msg}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试普通二维码生成
        async function testNormalQRCode() {
            const resultDiv = document.getElementById('normalResult');
            
            resultDiv.innerHTML = '正在生成普通二维码...';
            resultDiv.className = 'result loading';
            
            try {
                const requestData = {
                    clientTokens: ['TEST_001', 'TEST_002'],
                    page: 'pages/more/binding',
                    width: 430,
                    envVersion: 'release',
                    useTemplate: false
                };
                
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/qrcode/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const successCount = result.data.filter(item => item.status === 'success').length;
                    resultDiv.innerHTML = `✅ 普通二维码生成成功: ${successCount}/${result.data.length}`;
                    resultDiv.className = 'result success';
                    testQRCodes = result.data.filter(item => item.status === 'success');
                    document.getElementById('exportBtn').disabled = false;
                } else {
                    resultDiv.innerHTML = `❌ 生成失败: ${result.msg}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试模板二维码生成
        async function testTemplateQRCode() {
            const resultDiv = document.getElementById('templateResult');
            const tableDiv = document.getElementById('templateImagesTable');
            const tableBody = document.getElementById('templateImagesBody');

            resultDiv.innerHTML = '正在生成模板二维码...';
            resultDiv.className = 'result loading';
            tableDiv.style.display = 'none';

            try {
                const requestData = {
                    clientTokens: ['QDX_7XEVMDBEFO1Q', 'QDX_7XEVMDBEFO2Q', 'QDX_7XEVMDBEFZ1Q'],
                    page: 'pages/more/binding',
                    useTemplate: true
                };

                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/qrcode/batch-with-template`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (result.code === 0) {
                    const successCount = result.data.filter(item => item.status === 'success').length;
                    resultDiv.innerHTML = `✅ 模板二维码生成成功: ${successCount}/${result.data.length}`;
                    resultDiv.className = 'result success';
                    testQRCodes = result.data.filter(item => item.status === 'success');
                    document.getElementById('exportBtn').disabled = false;

                    // 显示图片表格
                    displayImagesInTable(result.data, tableBody);
                    tableDiv.style.display = 'block';
                } else {
                    resultDiv.innerHTML = `❌ 生成失败: ${result.msg}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试批量导出
        async function testBatchExport() {
            const resultDiv = document.getElementById('exportResult');
            
            if (testQRCodes.length === 0) {
                resultDiv.innerHTML = '❌ 请先生成二维码';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.innerHTML = '正在测试批量导出...';
            resultDiv.className = 'result loading';
            
            try {
                const qrCodes = testQRCodes.map(item => ({
                    clientToken: item.clientToken,
                    base64Data: item.qrCodeBase64
                }));
                
                const requestData = {
                    qrCodes: qrCodes,
                    zipFileName: 'test_export.zip'
                };
                
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/qrcode/batch-export`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'test_export.zip';
                    link.click();
                    URL.revokeObjectURL(url);
                    
                    resultDiv.innerHTML = `✅ 批量导出成功，文件大小: ${(blob.size / 1024).toFixed(2)} KB`;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ 导出失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 在表格中显示图片
        function displayImagesInTable(data, tableBody) {
            tableBody.innerHTML = '';

            data.forEach((item, index) => {
                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #ddd';

                if (item.status === 'success') {
                    row.innerHTML = `
                        <td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">${item.clientToken}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">
                            <img src="data:image/png;base64,${item.qrCodeBase64}"
                                 alt="二维码"
                                 style="max-width: 150px; height: auto; border: 1px solid #ccc; border-radius: 4px;"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                            <div style="display: none; color: #e74c3c; padding: 10px;">图片加载失败</div>
                        </td>
                        <td style="border: 1px solid #ddd; padding: 10px; color: #28a745;">✅ 成功</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">
                            <button onclick="downloadImage('${item.qrCodeBase64}', '${item.clientToken}')"
                                    style="background-color: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                                下载
                            </button>
                        </td>
                    `;
                } else {
                    row.innerHTML = `
                        <td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">${item.clientToken}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #6c757d;">无图片</td>
                        <td style="border: 1px solid #ddd; padding: 10px; color: #dc3545;">❌ ${item.errorMessage || '失败'}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">-</td>
                    `;
                }

                tableBody.appendChild(row);
            });
        }

        // 下载单个图片
        function downloadImage(base64Data, clientToken) {
            try {
                const link = document.createElement('a');
                link.href = `data:image/png;base64,${base64Data}`;
                link.download = `qrcode_${clientToken}.png`;
                link.click();
            } catch (error) {
                alert('下载失败: ' + error.message);
            }
        }

        // 页面加载时自动检查连接
        window.addEventListener('load', function() {
            checkConnection();
        });
    </script>
</body>
</html>
