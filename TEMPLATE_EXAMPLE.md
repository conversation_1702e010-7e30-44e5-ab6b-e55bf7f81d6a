# 模板合成功能使用示例

## 功能效果

使用模板合成功能后，生成的图片效果如下：

```
┌─────────────────────────────────────────────────────────┐
│  ┌─────────┐  计件宝365 (扫码绑定)                        │
│  │ QR CODE │  ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─  │
│  │         │                                           │
│  │    █    │  ID: USER_123456                          │
│  │  █ █ █  │                                           │
│  │    █    │                                           │
│  └─────────┘                                           │
└─────────────────────────────────────────────────────────┘
```

## 完整使用流程

### 1. 启动应用

```bash
# 确保已配置微信小程序信息
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

### 2. 调用API生成带模板的小程序码

```bash
curl -X POST http://localhost:8080/pc-api/wechat/qrcode/batch-with-template \
  -H "Content-Type: application/json" \
  -d '{
    "clientTokens": [
      "USER_001", 
      "CLIENT_ABC123", 
      "DEVICE_XYZ789"
    ],
    "page": "pages/more/binding",
    "width": 430,
    "envVersion": "release"
  }'
```

### 3. 响应示例

```json
{
  "code": 200,
  "data": [
    {
      "clientToken": "USER_001",
      "qrCodeBase64": "iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAYAAACAvzbMAAAA...",
      "status": "success",
      "fullPath": "pages/more/binding?clientToken=USER_001"
    },
    {
      "clientToken": "CLIENT_ABC123", 
      "qrCodeBase64": "iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAYAAACAvzbMAAAA...",
      "status": "success",
      "fullPath": "pages/more/binding?clientToken=CLIENT_ABC123"
    },
    {
      "clientToken": "DEVICE_XYZ789",
      "qrCodeBase64": "iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAYAAACAvzbMAAAA...",
      "status": "success", 
      "fullPath": "pages/more/binding?clientToken=DEVICE_XYZ789"
    }
  ],
  "msg": ""
}
```

### 4. 前端使用示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>小程序码展示</title>
</head>
<body>
    <div id="qrcode-container"></div>
    
    <script>
        // 调用API获取小程序码
        fetch('/pc-api/wechat/qrcode/batch-with-template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                clientTokens: ['USER_001', 'USER_002'],
                useTemplate: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                displayQRCodes(data.data);
            }
        });
        
        function displayQRCodes(qrCodes) {
            const container = document.getElementById('qrcode-container');
            
            qrCodes.forEach(item => {
                if (item.status === 'success') {
                    const img = document.createElement('img');
                    img.src = 'data:image/png;base64,' + item.qrCodeBase64;
                    img.alt = 'QR Code for ' + item.clientToken;
                    img.style.margin = '10px';
                    container.appendChild(img);
                }
            });
        }
    </script>
</body>
</html>
```

## Java代码示例

### 在Spring Boot服务中使用

```java
@Service
public class QRCodeService {
    
    @Autowired
    private WechatService wechatService;
    
    /**
     * 为用户生成专属小程序码
     */
    public String generateUserQRCode(String userId) {
        QRCodeReqVO request = new QRCodeReqVO();
        request.setClientTokens(Arrays.asList(userId));
        request.setPage("pages/more/binding");
        request.setUseTemplate(true); // 使用模板
        
        List<QRCodeRespVO> results = wechatService.generateQRCodes(request);
        
        if (!results.isEmpty() && "success".equals(results.get(0).getStatus())) {
            return results.get(0).getQrCodeBase64();
        }
        
        throw new RuntimeException("生成小程序码失败");
    }
    
    /**
     * 批量生成设备绑定码
     */
    public Map<String, String> generateDeviceQRCodes(List<String> deviceIds) {
        QRCodeReqVO request = new QRCodeReqVO();
        request.setClientTokens(deviceIds);
        request.setPage("pages/device/binding");
        request.setUseTemplate(true);
        
        List<QRCodeRespVO> results = wechatService.generateQRCodes(request);
        
        return results.stream()
            .filter(r -> "success".equals(r.getStatus()))
            .collect(Collectors.toMap(
                QRCodeRespVO::getClientToken,
                QRCodeRespVO::getQrCodeBase64
            ));
    }
}
```

### 直接使用工具类

```java
public class DirectUsageExample {
    
    public static void main(String[] args) {
        try {
            // 1. 生成原始小程序码
            String qrContent = "pages/more/binding?clientToken=TEST_USER";
            String originalQRCode = QRCodeUtil.generateQrCodeBase64(qrContent, 200, 200);
            
            // 2. 合成到模板
            String composedQRCode = ImageComposer.composeQrCodeWithTemplate(
                originalQRCode, 
                "TEST_USER"
            );
            
            // 3. 保存到文件
            byte[] imageBytes = Base64.getDecoder().decode(composedQRCode);
            Files.write(Paths.get("composed_qrcode.png"), imageBytes);
            
            System.out.println("合成完成！");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

## 自定义模板

### 1. 准备模板图片

创建一个PNG格式的模板图片，确保：
- 左侧有空白矩形区域用于放置二维码
- 有"ID:"文字标识
- 整体设计美观

### 2. 放置模板文件

将模板图片保存为：`src/main/resources/static/template.png`

### 3. 调整参数

根据您的模板图片，修改 `ImageComposer.java` 中的参数：

```java
// 二维码区域（根据模板中空白矩形的位置调整）
private static final int QR_CODE_X = 25;      // 左边距
private static final int QR_CODE_Y = 25;      // 上边距  
private static final int QR_CODE_WIDTH = 115; // 宽度
private static final int QR_CODE_HEIGHT = 115; // 高度

// 文字位置（根据模板中"ID:"的位置调整）
private static final int TEXT_X = 180;        // "ID:"的X坐标
private static final int TEXT_Y = 120;        // "ID:"的Y坐标
private static final int FONT_SIZE = 24;      // 字体大小
```

### 4. 测试效果

```bash
# 运行测试验证效果
mvn test -Dtest=ImageComposerTest#testImageComposer

# 查看生成的测试图片
ls test_composed_image.png
```

## 性能优化建议

### 1. 批量处理

```java
// 推荐：批量处理
List<String> tokens = Arrays.asList("user1", "user2", "user3");
QRCodeReqVO request = new QRCodeReqVO();
request.setClientTokens(tokens);
request.setUseTemplate(true);
List<QRCodeRespVO> results = wechatService.generateQRCodes(request);

// 不推荐：逐个处理
for (String token : tokens) {
    // 单独调用API...
}
```

### 2. 异步处理

```java
@Async
public CompletableFuture<List<QRCodeRespVO>> generateQRCodesAsync(QRCodeReqVO request) {
    return CompletableFuture.completedFuture(wechatService.generateQRCodes(request));
}
```

### 3. 缓存结果

```java
@Cacheable(value = "qrcodes", key = "#clientToken")
public String getCachedQRCode(String clientToken) {
    // 生成逻辑...
}
```

## 故障排除

### 常见问题及解决方案

1. **模板图片加载失败**
   ```
   错误：无法加载模板图片
   解决：检查 src/main/resources/static/template.png 是否存在
   ```

2. **二维码位置不正确**
   ```
   错误：二维码没有完全覆盖空白区域
   解决：调整 ImageComposer 中的 QR_CODE_X/Y/WIDTH/HEIGHT 参数
   ```

3. **文字位置偏移**
   ```
   错误：clientToken文字位置不合适
   解决：调整 TEXT_X/Y 参数，确保与"ID:"对齐
   ```

4. **内存不足**
   ```
   错误：OutOfMemoryError
   解决：减少批量处理数量，或增加JVM内存
   ```

### 调试技巧

1. **启用详细日志**
   ```yaml
   logging:
     level:
       com.quickdatax.demo.util.ImageComposer: DEBUG
   ```

2. **保存中间结果**
   ```java
   // 在测试中保存每个步骤的图片
   ImageComposerTest.testImageComposer();
   ```

3. **使用默认模板测试**
   ```java
   // 删除或重命名模板文件，使用默认模板测试
   mv src/main/resources/static/template.png template.png.bak
   ```

---

**提示：** 更多详细信息请参考 `TEMPLATE_USAGE_GUIDE.md` 文档。
