# 批量导出功能使用指南

## 功能概述

本功能提供了多种方式来批量导出生成的小程序码图片：

1. **单个下载** - 逐个下载每张图片
2. **批量下载** - 自动逐个下载所有图片
3. **前端打包ZIP** - 使用JavaScript在浏览器中打包ZIP文件
4. **服务端打包ZIP** - 在服务器端打包ZIP文件并下载
5. **预览模式** - 在新窗口中预览所有图片

## 使用方法

### 1. 生成小程序码

首先使用模板合成功能生成小程序码：

```bash
curl -X POST http://localhost:8080/pc-api/wechat/qrcode/batch-with-template \
  -H "Content-Type: application/json" \
  -d '{
    "clientTokens": ["USER_001", "USER_002", "USER_003"],
    "page": "pages/more/binding"
  }'
```

### 2. 前端页面操作

1. 打开 `frontend_example.html`
2. 勾选"使用绿色模板合成"
3. 输入客户端Token列表
4. 点击"生成小程序码"
5. 生成成功后，页面会显示：
   - 所有生成的图片
   - 统计信息（总数、成功、失败）
   - 批量操作按钮

### 3. 批量导出选项

#### 📥 批量下载图片
- 自动逐个下载所有成功生成的图片
- 每200ms下载一张，避免浏览器限制
- 文件名格式：`qrcode_[clientToken].png`

#### 📦 前端打包ZIP
- 使用JSZip库在浏览器中打包
- 自动加载JSZip库（如果未加载）
- 文件名格式：`qrcodes_batch_[时间戳].zip`

#### 🚀 服务端打包ZIP
- 在服务器端打包，处理速度更快
- 支持大批量图片处理
- 更稳定的ZIP文件生成

#### 👁️ 预览所有图片
- 在新窗口中展示所有图片
- 网格布局，便于查看
- 支持单独下载每张图片

## API接口详情

### 批量导出ZIP接口

**URL:** `POST /pc-api/wechat/qrcode/batch-export`

**请求参数:**
```json
{
  "qrCodes": [
    {
      "clientToken": "USER_001",
      "base64Data": "iVBORw0KGgoAAAA..."
    },
    {
      "clientToken": "USER_002", 
      "base64Data": "iVBORw0KGgoAAAA..."
    }
  ],
  "zipFileName": "qrcodes_batch_2024-07-04.zip"
}
```

**响应:**
- Content-Type: `application/octet-stream`
- Content-Disposition: `attachment; filename="qrcodes_batch_2024-07-04.zip"`
- 二进制ZIP文件数据

### JavaScript调用示例

```javascript
// 服务端打包下载
async function downloadServerZip(qrCodeResults) {
    const qrCodes = qrCodeResults
        .filter(item => item.status === 'success')
        .map(item => ({
            clientToken: item.clientToken,
            base64Data: item.qrCodeBase64
        }));
    
    const response = await fetch('/pc-api/wechat/qrcode/batch-export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            qrCodes: qrCodes,
            zipFileName: 'my_qrcodes.zip'
        })
    });
    
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'my_qrcodes.zip';
    link.click();
}
```

## 页面功能详解

### 图片渲染区域

生成成功后，页面会显示：

```html
<div class="results">
    <h3>生成结果：</h3>
    <div class="stats">总数：3 | 成功：3 | 失败：0</div>
    
    <!-- 批量操作按钮 -->
    <div class="batch-actions">
        <button onclick="batchDownloadImages()">📥 批量下载图片 (3张)</button>
        <button onclick="batchDownloadZip()">📦 前端打包ZIP</button>
        <button onclick="serverDownloadZip()">🚀 服务端打包ZIP</button>
        <button onclick="previewAllImages()">👁️ 预览所有图片</button>
    </div>
    
    <!-- 每个图片项 -->
    <div class="qrcode-item">
        <div class="token">Token: USER_001</div>
        <img src="data:image/png;base64,..." alt="小程序码" />
        <div class="path">pages/more/binding?clientToken=USER_001</div>
        <button onclick="downloadSingleQRCode(...)">💾 下载</button>
    </div>
</div>
```

### 样式特点

- **响应式布局** - 自适应不同屏幕尺寸
- **清晰展示** - 每张图片独立显示，包含Token和路径信息
- **操作便捷** - 多种下载方式，满足不同需求
- **状态反馈** - 实时显示操作进度和结果

## 技术实现

### 前端技术

1. **JSZip库** - 前端ZIP打包
   ```javascript
   // 动态加载JSZip
   function loadJSZip() {
       return new Promise((resolve, reject) => {
           const script = document.createElement('script');
           script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
           script.onload = resolve;
           script.onerror = reject;
           document.head.appendChild(script);
       });
   }
   ```

2. **Blob API** - 文件下载
   ```javascript
   const blob = new Blob([data], {type: 'application/zip'});
   const url = URL.createObjectURL(blob);
   ```

3. **Fetch API** - 服务端通信
   ```javascript
   const response = await fetch('/api/export', {
       method: 'POST',
       body: JSON.stringify(data)
   });
   ```

### 后端技术

1. **ZipOutputStream** - ZIP文件创建
   ```java
   ZipOutputStream zos = new ZipOutputStream(baos);
   ZipEntry zipEntry = new ZipEntry(fileName);
   zos.putNextEntry(zipEntry);
   zos.write(imageBytes);
   ```

2. **ResponseEntity** - 文件下载响应
   ```java
   HttpHeaders headers = new HttpHeaders();
   headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
   headers.setContentDispositionFormData("attachment", fileName);
   return ResponseEntity.ok().headers(headers).body(zipBytes);
   ```

## 性能优化

### 前端优化

1. **分批下载** - 避免浏览器并发限制
2. **延迟加载** - 按需加载JSZip库
3. **内存管理** - 及时释放Blob URL

### 后端优化

1. **流式处理** - 大文件流式写入
2. **文件名清理** - 防止路径注入攻击
3. **异常处理** - 单个文件失败不影响整体

## 故障排除

### 常见问题

1. **下载被浏览器阻止**
   ```
   问题：浏览器阻止多文件下载
   解决：允许弹窗，或使用ZIP打包方式
   ```

2. **JSZip加载失败**
   ```
   问题：CDN网络问题
   解决：使用服务端打包，或本地部署JSZip
   ```

3. **ZIP文件损坏**
   ```
   问题：Base64解码错误
   解决：检查图片数据完整性
   ```

4. **内存不足**
   ```
   问题：大批量图片处理
   解决：分批处理，或增加服务器内存
   ```

### 调试技巧

1. **查看控制台** - 检查JavaScript错误
2. **网络面板** - 监控API请求状态
3. **文件验证** - 下载后检查ZIP文件完整性

## 扩展功能

### 可能的扩展

1. **自定义文件名** - 支持用户自定义ZIP文件名
2. **选择性导出** - 支持选择部分图片导出
3. **格式转换** - 支持导出为PDF等其他格式
4. **云存储** - 支持直接上传到云存储服务

### 示例扩展代码

```javascript
// 选择性导出
function exportSelected() {
    const selected = document.querySelectorAll('.qrcode-item input:checked');
    const selectedData = Array.from(selected).map(checkbox => {
        const index = checkbox.dataset.index;
        return currentResults[index];
    });
    // 导出选中的图片...
}

// 自定义文件名
function customExport() {
    const fileName = prompt('请输入ZIP文件名:', 'my_qrcodes.zip');
    if (fileName) {
        serverDownloadZip(fileName);
    }
}
```

---

**开发者:** freeze  
**日期:** 2024-07-04  
**版本:** v1.0
