<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序码生成示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #07c160;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #06ad56;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
        }
        .qrcode-item {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
            background: #fafafa;
        }
        .qrcode-item img {
            max-width: 200px;
            height: auto;
            border: 1px solid #eee;
        }
        .qrcode-item .token {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .qrcode-item .path {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            word-break: break-all;
        }
        .qrcode-item .download-btn {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
        }
        .qrcode-item .download-btn:hover {
            background-color: #2980b9;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }
        .success {
            color: #27ae60;
            background: #f2fdf5;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .batch-actions {
            margin: 20px 0;
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .batch-btn {
            background-color: #17a2b8;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
        }
        .batch-btn:hover {
            background-color: #138496;
        }
        .batch-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .stats {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序码批量生成</h1>
        <p>输入多个客户端Token，系统将为每个Token生成对应的小程序码</p>
        
        <form id="qrcodeForm">
            <div class="form-group">
                <label for="clientTokens">客户端Token列表（每行一个）：</label>
                <textarea id="clientTokens" placeholder="请输入客户端Token，每行一个，例如：&#10;user_123&#10;client_456&#10;token_789" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="page">小程序页面路径：</label>
                <input type="text" id="page" value="pages/more/binding" placeholder="例如：pages/more/binding">
            </div>
            
            <div class="form-group">
                <label for="width">二维码宽度（px）：</label>
                <input type="number" id="width" value="430" min="280" max="1280">
            </div>
            
            <div class="form-group">
                <label for="envVersion">小程序版本：</label>
                <select id="envVersion">
                    <option value="release">正式版</option>
                    <option value="trial">体验版</option>
                    <option value="develop">开发版</option>
                </select>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="useTemplate" checked> 使用绿色模板合成
                </label>
                <small style="display: block; color: #666; margin-top: 5px;">
                    勾选后将把小程序码合成到绿色模板上，并在ID后显示Token
                </small>
            </div>

            <button type="submit" id="generateBtn">生成小程序码</button>
            <button type="button" onclick="clearResults()">清空结果</button>
        </form>
        
        <div id="message"></div>
        <div id="results" class="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:47081'; // 根据实际情况修改

        // 检查API连接状态
        async function checkApiConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/access-token`);
                if (response.ok) {
                    console.log('API连接正常');
                    return true;
                } else {
                    console.warn('API连接异常，状态码:', response.status);
                    return false;
                }
            } catch (error) {
                console.error('API连接失败:', error);
                showMessage('无法连接到服务器，请检查服务是否启动', 'error');
                return false;
            }
        }
        
        document.getElementById('qrcodeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 检查API连接
            const isConnected = await checkApiConnection();
            if (!isConnected) {
                return;
            }

            const clientTokensText = document.getElementById('clientTokens').value.trim();
            if (!clientTokensText) {
                showMessage('请输入客户端Token', 'error');
                return;
            }

            const clientTokens = clientTokensText.split('\n')
                .map(token => token.trim())
                .filter(token => token.length > 0);

            if (clientTokens.length === 0) {
                showMessage('请输入有效的客户端Token', 'error');
                return;
            }

            if (clientTokens.length > 100) {
                showMessage('一次最多生成100个小程序码', 'error');
                return;
            }

            const useTemplate = document.getElementById('useTemplate').checked;

            const requestData = {
                clientTokens: clientTokens,
                page: document.getElementById('page').value || 'pages/more/binding',
                width: parseInt(document.getElementById('width').value) || 430,
                envVersion: document.getElementById('envVersion').value,
                useTemplate: useTemplate
            };

            await generateQRCodes(requestData);
        });
        
        async function generateQRCodes(requestData) {
            const generateBtn = document.getElementById('generateBtn');
            const messageDiv = document.getElementById('message');
            const resultsDiv = document.getElementById('results');
            
            try {
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';
                
                showMessage('正在生成小程序码，请稍候...', 'loading');
                resultsDiv.innerHTML = '';
                
                // 根据是否使用模板选择不同的API端点
                const endpoint = requestData.useTemplate ? '/pc-api/wechat/qrcode/batch-with-template' : '/pc-api/wechat/qrcode/batch';

                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.code === 200) {
                    displayResults(result.data);
                    const successCount = result.data.filter(item => item.status === 'success').length;
                    const totalCount = result.data.length;
                    showMessage(`生成完成！成功：${successCount}，总数：${totalCount}`, 'success');
                } else {
                    showMessage(`生成失败：${result.msg || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('生成小程序码失败:', error);
                showMessage(`网络错误：${error.message}`, 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '生成小程序码';
            }
        }
        
        // 全局变量存储生成的结果
        let currentResults = [];

        function displayResults(results) {
            currentResults = results; // 保存结果用于批量导出

            const resultsDiv = document.getElementById('results');

            // 统计信息
            const successCount = results.filter(item => item.status === 'success').length;
            const failedCount = results.length - successCount;

            resultsDiv.innerHTML = `
                <h3>生成结果：</h3>
                <div class="stats">
                    总数：${results.length} | 成功：${successCount} | 失败：${failedCount}
                </div>
                ${successCount > 0 ? `
                <div class="batch-actions">
                    <button class="batch-btn" onclick="batchDownloadImages()">
                        📥 批量下载图片 (${successCount}张)
                    </button>
                    <button class="batch-btn" onclick="batchDownloadZip()">
                        📦 前端打包ZIP
                    </button>
                    <button class="batch-btn" onclick="serverDownloadZip()">
                        🚀 服务端打包ZIP
                    </button>
                    <button class="batch-btn" onclick="previewAllImages()">
                        👁️ 预览所有图片
                    </button>
                </div>
                ` : ''}
            `;

            // 渲染每个结果
            results.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'qrcode-item';
                itemDiv.id = `qrcode-item-${index}`;

                if (item.status === 'success') {
                    itemDiv.innerHTML = `
                        <div class="token">Token: ${item.clientToken}</div>
                        <img src="data:image/png;base64,${item.qrCodeBase64}" alt="小程序码" style="max-width: 200px; height: auto; border: 1px solid #ddd; border-radius: 4px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                        <div style="display: none; color: #e74c3c; padding: 20px; border: 1px solid #e74c3c; border-radius: 4px;">图片加载失败</div>
                        <div class="path">${item.fullPath}</div>
                        <button class="download-btn" onclick="downloadSingleQRCode('${item.qrCodeBase64}', '${item.clientToken}')">
                            💾 下载
                        </button>
                    `;
                } else {
                    itemDiv.innerHTML = `
                        <div class="token">Token: ${item.clientToken}</div>
                        <div class="error">生成失败：${item.errorMessage}</div>
                    `;
                }

                resultsDiv.appendChild(itemDiv);
            });
        }
        
        // 下载单个二维码
        function downloadSingleQRCode(base64Data, clientToken) {
            const link = document.createElement('a');
            link.href = 'data:image/png;base64,' + base64Data;
            link.download = `qrcode_${clientToken}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 批量下载图片（逐个下载）
        function batchDownloadImages() {
            const successResults = currentResults.filter(item => item.status === 'success');

            if (successResults.length === 0) {
                showMessage('没有可下载的图片', 'error');
                return;
            }

            showMessage(`开始下载 ${successResults.length} 张图片...`, 'loading');

            successResults.forEach((item, index) => {
                setTimeout(() => {
                    downloadSingleQRCode(item.qrCodeBase64, item.clientToken);

                    if (index === successResults.length - 1) {
                        setTimeout(() => {
                            showMessage(`批量下载完成！共下载 ${successResults.length} 张图片`, 'success');
                        }, 500);
                    }
                }, index * 200); // 每200ms下载一张，避免浏览器限制
            });
        }

        // 打包下载ZIP（使用JSZip库）
        async function batchDownloadZip() {
            const successResults = currentResults.filter(item => item.status === 'success');

            if (successResults.length === 0) {
                showMessage('没有可下载的图片', 'error');
                return;
            }

            try {
                showMessage('正在打包ZIP文件...', 'loading');

                // 检查是否已加载JSZip
                if (typeof JSZip === 'undefined') {
                    await loadJSZip();
                }

                const zip = new JSZip();
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

                // 添加文件到ZIP
                successResults.forEach(item => {
                    const imageData = item.qrCodeBase64;
                    zip.file(`qrcode_${item.clientToken}.png`, imageData, {base64: true});
                });

                // 生成ZIP文件
                const content = await zip.generateAsync({type: 'blob'});

                // 下载ZIP文件
                const link = document.createElement('a');
                link.href = URL.createObjectURL(content);
                link.download = `qrcodes_batch_${timestamp}.zip`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showMessage(`ZIP文件下载完成！包含 ${successResults.length} 张图片`, 'success');

            } catch (error) {
                console.error('ZIP打包失败:', error);
                showMessage('ZIP打包失败，请尝试批量下载', 'error');
            }
        }

        // 动态加载JSZip库
        function loadJSZip() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // 预览所有图片（新窗口）
        function previewAllImages() {
            const successResults = currentResults.filter(item => item.status === 'success');

            if (successResults.length === 0) {
                showMessage('没有可预览的图片', 'error');
                return;
            }

            const previewWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes');

            let htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>小程序码预览 - ${successResults.length}张</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
                        .item { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center; }
                        .item img { max-width: 100%; height: auto; border: 1px solid #ddd; }
                        .token { font-weight: bold; margin-bottom: 10px; color: #333; }
                        .path { font-size: 12px; color: #666; margin-top: 10px; word-break: break-all; }
                        .download-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px; }
                        .download-btn:hover { background: #0056b3; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>小程序码预览</h1>
                        <p>共 ${successResults.length} 张图片 | 生成时间: ${new Date().toLocaleString()}</p>
                    </div>
                    <div class="grid">
            `;

            successResults.forEach(item => {
                htmlContent += `
                    <div class="item">
                        <div class="token">Token: ${item.clientToken}</div>
                        <img src="data:image/png;base64,${item.qrCodeBase64}" alt="小程序码" />
                        <div class="path">${item.fullPath}</div>
                        <button class="download-btn" onclick="downloadImage('${item.qrCodeBase64}', '${item.clientToken}')">下载此图</button>
                    </div>
                `;
            });

            htmlContent += `
                    </div>
                    <script>
                        function downloadImage(base64Data, clientToken) {
                            const link = document.createElement('a');
                            link.href = 'data:image/png;base64,' + base64Data;
                            link.download = 'qrcode_' + clientToken + '.png';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }
                    </script>
                </body>
                </html>
            `;

            previewWindow.document.write(htmlContent);
            previewWindow.document.close();
        }

        // 服务端打包下载ZIP
        async function serverDownloadZip() {
            const successResults = currentResults.filter(item => item.status === 'success');

            if (successResults.length === 0) {
                showMessage('没有可下载的图片', 'error');
                return;
            }

            try {
                showMessage('正在请求服务端打包ZIP...', 'loading');

                // 准备请求数据
                const qrCodes = successResults.map(item => ({
                    clientToken: item.clientToken,
                    base64Data: item.qrCodeBase64
                }));

                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const requestData = {
                    qrCodes: qrCodes,
                    zipFileName: `qrcodes_batch_${timestamp}.zip`
                };

                // 发送请求到服务端
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/qrcode/batch-export`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`服务器错误: ${response.status}`);
                }

                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let fileName = `qrcodes_batch_${timestamp}.zip`;
                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (fileNameMatch) {
                        fileName = fileNameMatch[1];
                    }
                }

                // 下载文件
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                showMessage(`服务端ZIP下载完成！包含 ${successResults.length} 张图片`, 'success');

            } catch (error) {
                console.error('服务端ZIP下载失败:', error);
                showMessage(`服务端下载失败: ${error.message}，请尝试前端打包`, 'error');
            }
        }
        
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            
            if (type === 'loading') {
                messageDiv.innerHTML = `<div class="loading">${message}</div>`;
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('message').innerHTML = '';
            currentResults = []; // 清空结果数据
        }
        
        // 示例数据填充
        function fillExample() {
            document.getElementById('clientTokens').value = 'user_001\nuser_002\nuser_003\nclient_abc\ntoken_xyz';
        }
        
        // 页面加载完成后添加示例按钮和检查连接
        window.addEventListener('load', async function() {
            const form = document.getElementById('qrcodeForm');
            const exampleBtn = document.createElement('button');
            exampleBtn.type = 'button';
            exampleBtn.textContent = '填充示例数据';
            exampleBtn.onclick = fillExample;
            exampleBtn.style.backgroundColor = '#3498db';
            form.appendChild(exampleBtn);

            // 检查API连接状态
            showMessage('正在检查服务器连接...', 'loading');
            const isConnected = await checkApiConnection();
            if (isConnected) {
                showMessage('服务器连接正常，可以开始使用', 'success');
                setTimeout(() => {
                    document.getElementById('message').innerHTML = '';
                }, 3000);
            } else {
                showMessage('服务器连接失败，请检查服务是否启动在端口47081', 'error');
            }
        });
    </script>
</body>
</html>
