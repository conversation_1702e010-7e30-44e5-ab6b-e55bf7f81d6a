<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序码生成示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #07c160;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #06ad56;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
        }
        .qrcode-item {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
            background: #fafafa;
        }
        .qrcode-item img {
            max-width: 200px;
            height: auto;
            border: 1px solid #eee;
        }
        .qrcode-item .token {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .qrcode-item .path {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            word-break: break-all;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }
        .success {
            color: #27ae60;
            background: #f2fdf5;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序码批量生成</h1>
        <p>输入多个客户端Token，系统将为每个Token生成对应的小程序码</p>
        
        <form id="qrcodeForm">
            <div class="form-group">
                <label for="clientTokens">客户端Token列表（每行一个）：</label>
                <textarea id="clientTokens" placeholder="请输入客户端Token，每行一个，例如：&#10;user_123&#10;client_456&#10;token_789" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="page">小程序页面路径：</label>
                <input type="text" id="page" value="pages/more/binding" placeholder="例如：pages/more/binding">
            </div>
            
            <div class="form-group">
                <label for="width">二维码宽度（px）：</label>
                <input type="number" id="width" value="430" min="280" max="1280">
            </div>
            
            <div class="form-group">
                <label for="envVersion">小程序版本：</label>
                <select id="envVersion">
                    <option value="release">正式版</option>
                    <option value="trial">体验版</option>
                    <option value="develop">开发版</option>
                </select>
            </div>
            
            <button type="submit" id="generateBtn">生成小程序码</button>
            <button type="button" onclick="clearResults()">清空结果</button>
        </form>
        
        <div id="message"></div>
        <div id="results" class="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080'; // 根据实际情况修改
        
        document.getElementById('qrcodeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const clientTokensText = document.getElementById('clientTokens').value.trim();
            if (!clientTokensText) {
                showMessage('请输入客户端Token', 'error');
                return;
            }
            
            const clientTokens = clientTokensText.split('\n')
                .map(token => token.trim())
                .filter(token => token.length > 0);
            
            if (clientTokens.length === 0) {
                showMessage('请输入有效的客户端Token', 'error');
                return;
            }
            
            if (clientTokens.length > 100) {
                showMessage('一次最多生成100个小程序码', 'error');
                return;
            }
            
            const requestData = {
                clientTokens: clientTokens,
                page: document.getElementById('page').value || 'pages/more/binding',
                width: parseInt(document.getElementById('width').value) || 430,
                envVersion: document.getElementById('envVersion').value
            };
            
            await generateQRCodes(requestData);
        });
        
        async function generateQRCodes(requestData) {
            const generateBtn = document.getElementById('generateBtn');
            const messageDiv = document.getElementById('message');
            const resultsDiv = document.getElementById('results');
            
            try {
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';
                
                showMessage('正在生成小程序码，请稍候...', 'loading');
                resultsDiv.innerHTML = '';
                
                const response = await fetch(`${API_BASE_URL}/pc-api/wechat/qrcode/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    displayResults(result.data);
                    const successCount = result.data.filter(item => item.status === 'success').length;
                    const totalCount = result.data.length;
                    showMessage(`生成完成！成功：${successCount}，总数：${totalCount}`, 'success');
                } else {
                    showMessage(`生成失败：${result.msg}`, 'error');
                }
            } catch (error) {
                console.error('生成小程序码失败:', error);
                showMessage(`网络错误：${error.message}`, 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '生成小程序码';
            }
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>生成结果：</h3>';
            
            results.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'qrcode-item';
                
                if (item.status === 'success') {
                    itemDiv.innerHTML = `
                        <div class="token">Token: ${item.clientToken}</div>
                        <img src="data:image/png;base64,${item.qrCodeBase64}" alt="小程序码" />
                        <div class="path">${item.fullPath}</div>
                        <button onclick="downloadQRCode('${item.qrCodeBase64}', '${item.clientToken}')">下载</button>
                    `;
                } else {
                    itemDiv.innerHTML = `
                        <div class="token">Token: ${item.clientToken}</div>
                        <div class="error">生成失败：${item.errorMessage}</div>
                    `;
                }
                
                resultsDiv.appendChild(itemDiv);
            });
        }
        
        function downloadQRCode(base64Data, clientToken) {
            const link = document.createElement('a');
            link.href = 'data:image/png;base64,' + base64Data;
            link.download = `qrcode_${clientToken}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            
            if (type === 'loading') {
                messageDiv.innerHTML = `<div class="loading">${message}</div>`;
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('message').innerHTML = '';
        }
        
        // 示例数据填充
        function fillExample() {
            document.getElementById('clientTokens').value = 'user_001\nuser_002\nuser_003\nclient_abc\ntoken_xyz';
        }
        
        // 页面加载完成后添加示例按钮
        window.addEventListener('load', function() {
            const form = document.getElementById('qrcodeForm');
            const exampleBtn = document.createElement('button');
            exampleBtn.type = 'button';
            exampleBtn.textContent = '填充示例数据';
            exampleBtn.onclick = fillExample;
            exampleBtn.style.backgroundColor = '#3498db';
            form.appendChild(exampleBtn);
        });
    </script>
</body>
</html>
