# 微信小程序码生成功能实现

## 功能概述

本项目实现了微信小程序码的批量生成功能，完全按照微信官方文档规范开发。用户扫描生成的小程序码后，会跳转到指定的小程序页面并携带相应的参数。

## 核心特性

✅ **批量生成** - 支持一次性生成多个小程序码  
✅ **参数传递** - 扫码后自动跳转到指定页面并携带clientToken参数  
✅ **官方API** - 基于微信官方"获取不限制的小程序码"接口  
✅ **自动缓存** - Access Token自动获取和缓存管理  
✅ **错误处理** - 完善的异常处理和错误信息返回  
✅ **Base64输出** - 返回Base64编码的图片，便于前端使用  

## 技术架构

### 后端技术栈
- **Spring Boot 3.0.2** - 主框架
- **Spring WebFlux** - HTTP客户端，用于调用微信API
- **Hutool** - 工具库，提供JSON处理和Base64编码
- **Lombok** - 简化代码编写

### 核心组件

1. **WechatProperties** - 微信配置管理
2. **WechatService** - 核心业务逻辑
3. **WechatController** - REST API接口
4. **QRCodeReqVO/QRCodeRespVO** - 请求响应数据模型

## 文件结构

```
src/main/java/com/quickdatax/demo/
├── web/
│   ├── config/
│   │   └── WechatProperties.java          # 微信配置类
│   ├── controller/
│   │   ├── vo/
│   │   │   ├── QRCodeReqVO.java          # 请求VO
│   │   │   └── QRCodeRespVO.java         # 响应VO
│   │   └── WechatController.java         # 控制器
│   └── service/
│       └── WechatService.java            # 核心服务
├── util/
│   └── QRCodeUtil.java                   # 二维码工具类（增强版）
└── resources/
    └── application-local.yaml            # 配置文件

其他文件：
├── API_USAGE_EXAMPLE.md                  # API使用说明
├── frontend_example.html                # 前端示例页面
├── README_WECHAT_QRCODE.md              # 本文档
└── src/test/java/com/quickdatax/demo/
    └── WechatServiceTest.java            # 测试类
```

## 快速开始

### 1. 配置微信小程序信息

编辑 `src/main/resources/application-local.yaml`：

```yaml
quickdatax:
  wechat:
    app-id: wx1234567890abcdef        # 您的小程序AppID
    app-secret: your_app_secret_here  # 您的小程序AppSecret
    api-base-url: https://api.weixin.qq.com
    access-token-cache-time: 7000
```

### 2. 启动应用

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

### 3. 测试API

使用curl测试：

```bash
curl -X POST http://localhost:8080/pc-api/wechat/qrcode/batch \
  -H "Content-Type: application/json" \
  -d '{
    "clientTokens": ["user123", "user456"],
    "page": "pages/more/binding",
    "width": 430,
    "envVersion": "release"
  }'
```

### 4. 使用前端示例

打开 `frontend_example.html` 文件，在浏览器中测试完整功能。

## API接口详情

### 批量生成小程序码

**URL:** `POST /pc-api/wechat/qrcode/batch`

**请求参数:**
```json
{
  "clientTokens": ["token1", "token2"],     // 必填：客户端Token数组
  "page": "pages/more/binding",             // 可选：小程序页面路径
  "width": 430,                             // 可选：二维码宽度
  "checkPath": true,                        // 可选：是否检查页面路径
  "envVersion": "release"                   // 可选：小程序版本
}
```

**响应格式:**
```json
{
  "code": 200,
  "data": [
    {
      "clientToken": "token1",
      "qrCodeBase64": "iVBORw0KGgoAAAA...",
      "status": "success",
      "fullPath": "pages/more/binding?clientToken=token1"
    }
  ],
  "msg": ""
}
```

### 获取Access Token

**URL:** `GET /pc-api/wechat/access-token`

用于调试和验证微信配置是否正确。

## 小程序端集成

在小程序的目标页面（如 `pages/more/binding`）中获取参数：

```javascript
Page({
  onLoad(query) {
    // 获取扫码传入的clientToken参数
    const clientToken = query.clientToken;
    console.log('客户端Token:', clientToken);
    
    // 执行绑定逻辑
    this.handleBinding(clientToken);
  },
  
  handleBinding(clientToken) {
    // 根据clientToken执行相应的业务逻辑
    // 例如：用户绑定、设备配对等
  }
})
```

## 核心实现原理

### 1. Access Token管理

```java
// 自动缓存Access Token，避免频繁调用微信API
private static final ConcurrentHashMap<String, AccessTokenCache> ACCESS_TOKEN_CACHE = new ConcurrentHashMap<>();
```

### 2. 小程序码生成

```java
// 调用微信官方API生成小程序码
String url = wechatProperties.getApiBaseUrl() + "/wxa/getwxacodeunlimit?access_token=" + accessToken;

JSONObject requestBody = new JSONObject();
requestBody.set("scene", "clientToken=" + clientToken);  // 关键：参数传递
requestBody.set("page", reqVO.getPage());
```

### 3. 错误处理

- 自动检测微信API返回的错误信息
- 区分成功和失败的生成结果
- 提供详细的错误信息

## 注意事项

1. **频率限制** - 微信API限制5000次/分钟
2. **参数限制** - scene参数最大32个字符
3. **页面验证** - 确保小程序页面路径存在
4. **网络超时** - 设置了30秒的请求超时
5. **内存限制** - WebClient配置了10MB的内存限制

## 测试验证

运行测试类验证功能：

```bash
mvn test -Dtest=WechatServiceTest
```

## 扩展功能

可以基于此实现扩展以下功能：

- 小程序码样式自定义（颜色、透明度等）
- 批量下载功能
- 生成记录存储
- 统计分析功能
- 定时清理缓存

## 故障排除

### 常见问题

1. **40001错误** - 检查AppID和AppSecret配置
2. **41030错误** - 检查小程序页面路径是否正确
3. **网络超时** - 检查网络连接和防火墙设置
4. **参数过长** - 确保clientToken长度不超过限制

### 调试建议

1. 先调用 `/pc-api/wechat/access-token` 验证配置
2. 查看应用日志获取详细错误信息
3. 使用微信开发者工具测试小程序页面
4. 检查微信公众平台的接口权限设置

## 版本历史

- **v1.0** - 基础功能实现，支持批量生成小程序码
- 支持参数传递和错误处理
- 完整的前端示例和文档

---

**开发者:** freeze  
**日期:** 2024-07-04  
**基于:** 微信小程序官方API文档
